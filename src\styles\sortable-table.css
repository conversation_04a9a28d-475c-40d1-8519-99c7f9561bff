/* 拖拽手柄样式优化 */
.drag-handle {
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  border-radius: 4px;
  position: relative;
}

.drag-handle:hover {
  color: #1890ff !important;
  background-color: #f0f9ff !important;
  border-color: #91caff !important;
  transform: scale(1.1);
}

.drag-handle:active {
  color: #096dd9 !important;
  background-color: #e6f4ff !important;
  border-color: #69b1ff !important;
  transform: scale(0.95);
  cursor: grabbing !important;
}

/* 拖拽行样式优化 */
.dragging-row {
  cursor: grabbing !important;
  user-select: none;
}

.dragging-row * {
  cursor: grabbing !important;
}

.dragging-row td {
  border-color: #d9d9d9 !important;
}

/* 表格行 hover 效果优化 */
.ant-table-tbody > tr {
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-table-tbody > tr:hover {
  background-color: #fafafa;
}

.ant-table-tbody > tr:hover .drag-handle {
  opacity: 1;
  visibility: visible;
}

/* 非 hover 状态下的拖拽手柄样式 */
.ant-table-tbody > tr:not(:hover) .drag-handle {
  opacity: 0.6;
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 全局拖拽时的鼠标样式 */
body.dragging {
  cursor: grabbing !important;
}

body.dragging * {
  cursor: grabbing !important;
}

/* 排序模式提示条样式优化 */
.sort-mode-tip {
  background: linear-gradient(90deg, #f0f9ff 0%, #ffffff 100%);
  border: 1px solid #91caff;
  border-radius: 6px;
}

/* 拖拽覆盖层样式优化 */
.drag-overlay {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
  min-width: 200px;
  z-index: 9999;
}

.drag-overlay-content {
  padding: 12px 16px;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
}

.drag-overlay-title {
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  font-size: 14px;
}

.drag-overlay-meta {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-overlay-id {
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 表格排序模式下的额外样式 */
.ant-table-tbody > tr.sortable-row {
  position: relative;
}

.ant-table-tbody > tr.sortable-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-table-tbody > tr.sortable-row:hover::before {
  background: #1890ff;
}

/* 优化拖拽时的视觉层次 */
.ant-table-container {
  position: relative;
}

.dragging-row {
  z-index: 1000;
}

/* 拖拽时其他行的淡化效果 */
body.dragging .ant-table-tbody > tr:not(.dragging-row) {
  opacity: 0.5;
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 拖拽插入指示器 */
tr.drop-indicator {
  position: relative !important;
}

tr.drop-indicator::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: #1890ff !important;
  z-index: 10000 !important;
  border-radius: 2px !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4) !important;
} 