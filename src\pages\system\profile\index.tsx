import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Descriptions,
  Modal,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../../../utils/authStore';
import { encryptPassword } from '../../../utils/crypto';
import { updatePassword } from '../../../services/system/auth';
import { showError, showSuccess } from '../../../utils/errorHandler';

const ProfilePage: React.FC = () => {
  const { user } = useAuthStore();
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleChangePassword = async () => {
    try {
      const values = await passwordForm.validateFields();
      setLoading(true);
      
      // 加密密码后调用API
      const encryptedData = {
        old_password: encryptPassword(values.oldPassword),
        new_password: encryptPassword(values.newPassword),
      };
      
      await updatePassword(encryptedData);
      
      showSuccess('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error: unknown) {
      showError(error, '密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <p>用户信息加载中...</p>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Card
        title="个人资料"
        extra={
          <Button
            type="primary"
            icon={<LockOutlined />}
            onClick={() => setPasswordModalVisible(true)}
          >
            修改密码
          </Button>
        }
      >
        <div style={{ display: 'flex', gap: '24px' }}>
          {/* 头像区域 */}
          <div style={{ textAlign: 'center', minWidth: '120px' }}>
            <Avatar
              size={80}
              icon={<UserOutlined />}
              style={{ marginBottom: '16px' }}
            />
            <div style={{ fontSize: '16px', fontWeight: '500', color: '#262626' }}>
              {user.name}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '4px' }}>
              {user.role === 0 ? '超级管理员' : '普通管理员'}
            </div>
          </div>

          {/* 信息区域 */}
          <div style={{ flex: 1 }}>
            <Descriptions column={1} styles={{ label: { width: '100px' } }}>
              <Descriptions.Item label="用户ID">
                {user.uid}
              </Descriptions.Item>
              <Descriptions.Item label="用户名">
                {user.username}
              </Descriptions.Item>
              <Descriptions.Item label="姓名">
                {user.name}
              </Descriptions.Item>
              <Descriptions.Item label="角色">
                {user.role === 0 ? '超级管理员' : '普通管理员'}
              </Descriptions.Item>
              {user.tenant_id && (
                <Descriptions.Item label="租户ID">
                  {user.tenant_id}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        </div>
      </Card>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        onOk={handleChangePassword}
        confirmLoading={loading}
        destroyOnHidden
      >
        <Form
          form={passwordForm}
          layout="vertical"
        >
          <Form.Item
            name="oldPassword"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProfilePage; 