import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Tag, Avatar, message, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, FileImageOutlined, DragOutlined, SaveOutlined, CloseOutlined, EyeOutlined, HomeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { TableProps } from 'antd/es/table';
import { 
  getFrameworks, 
  updateFramework,
  batchUpdateFrameworkOrder,
  type FrameworkResponse
} from '../../../services/system/framework';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import FrameworkForm from './FrameworkForm';
import LogoUploader from './LogoUploader';
import SortableFrameworkTable from './SortableFrameworkTable';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;

const FrameworkManagement: React.FC = () => {
  const [frameworks, setFrameworks] = useState<FrameworkResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [modalVisible, setModalVisible] = useState(false);
  const [logoUploaderVisible, setLogoUploaderVisible] = useState(false);
  const [currentFramework, setCurrentFramework] = useState<FrameworkResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储名称输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [tableKey, setTableKey] = useState<number>(0);
  
    // 排序模式相关状态
  const [isSortMode, setIsSortMode] = useState(false);
  const [originalFrameworks, setOriginalFrameworks] = useState<FrameworkResponse[]>([]);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);

  // 定义状态映射
  const statusMap: { [key: number]: { name: string; color: string } } = {
    0: { name: '禁用', color: 'red' },
    1: { name: '启用', color: 'green' },
  };

  // 进入排序模式
  const enterSortMode = () => {
    if (isRecycleBin) {
      message.warning('回收站模式下不能进行排序');
      return;
    }
    if (!selectedTenantId) {
      message.warning('请先选择租户');
      return;
    }
    
    // 保存原始数据用于取消时恢复
    setOriginalFrameworks([...frameworks]);
    setIsSortMode(true);
  };

  // 退出排序模式
  const exitSortMode = () => {
    setIsSortMode(false);
    setOriginalFrameworks([]);
  };

  // 保存排序
  const handleSortSave = async () => {
    if (loading) return; // 防止重复提交
    
    try {
      setLoading(true);
      
      // 检查是否有实际变化
      const hasChanges = frameworks.some((framework, index) => {
        const originalIndex = originalFrameworks.findIndex(orig => orig.id === framework.id);
        return originalIndex !== index;
      });
      
      if (!hasChanges) {
        message.info('排序未发生变化');
        exitSortMode();
        return;
      }
      
      // 准备批量更新数据
      const frameworkUpdates = frameworks.map((framework, index) => ({
        id: framework.id,
        priority: index + 1 // 优先级从小到大，第一个项目priority=1，排在最前面
      }));
      
      // 调用批量更新接口
      const response = await batchUpdateFrameworkOrder({
        tenant_id: selectedTenantId!,
        frameworks: frameworkUpdates
      });
      
      showSuccess(`排序保存成功，共更新 ${response.success_count} 个项目`);
      
      // 重新获取数据
      await fetchFrameworks();
      exitSortMode();
    } catch (error) {
      showError(error, '保存排序失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消排序的回调
  const handleSortCancel = () => {
    setFrameworks([...originalFrameworks]);
    exitSortMode();
  };

  // 框架数据变化的回调
  const handleFrameworksChange = (newFrameworks: FrameworkResponse[]) => {
    setFrameworks(newFrameworks);
  };

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取理论框架列表
  const fetchFrameworks = useCallback(async () => {
    if (!selectedTenantId) {
      setFrameworks([]);
      return;
    }

    try {
      setLoading(true);
      
      // 始终按照 priority 从小到大排序
      const sortBy = 'priority';
      const sortOrder = 'asc';
      
      const response = await getFrameworks(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder
      });
      
      // 手动过滤数据（因为接口不支持name查询）
      let filteredFrameworks = [...response.items];
      
      // 手动实现名称搜索过滤
      if (searchName) {
        filteredFrameworks = filteredFrameworks.filter(framework => 
          framework.name.toLowerCase().includes(searchName.toLowerCase())
        );
      }
      
      setFrameworks(filteredFrameworks);
      setPagination(prev => ({
        ...prev,
        total: searchName ? filteredFrameworks.length : response.total
      }));
    } catch (error) {
      showError(error, '获取理论框架列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchFrameworks();
    }
  }, [fetchFrameworks]);

  // 处理名称搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理名称搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加理论框架
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentFramework(null);
    setModalVisible(true);
  };

  // 处理编辑理论框架
  const handleEdit = (record: FrameworkResponse) => {
    setCurrentFramework(record);
    setModalVisible(true);
  };

  // 处理查看详情
  const handleViewDetail = (record: FrameworkResponse) => {
    navigate(`/system/module?framework_id=${record.id}&framework_name=${encodeURIComponent(record.name)}`);
  };

  // 处理上传LOGO
  const handleUploadLogo = (record: FrameworkResponse) => {
    setCurrentFramework(record);
    setLogoUploaderVisible(true);
  };

  // 处理禁用理论框架（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个理论框架吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将理论框架禁用（设置active为0）
          await updateFramework(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchFrameworks();
        } catch (error: unknown) {
          showError(error, `${actionText}理论框架失败`);
        }
      },
    });
  };

  // 处理恢复理论框架
  const handleRestore = async (frameworkId: number) => {
    try {
      await updateFramework(frameworkId, { active: 1 });
      showSuccess('恢复成功');
      fetchFrameworks();
    } catch (error) {
      showError(error, '恢复理论框架失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchFrameworks();
  };

  // LOGO上传成功后的回调
  const handleLogoUploadSuccess = () => {
    setLogoUploaderVisible(false);
    fetchFrameworks();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 显式调用接口重新获取数据
    if (selectedTenantId) {
      await fetchFrameworks();
    }
  };

  // 处理表格变化（只保留分页处理）
  const handleTableChange: TableProps<FrameworkResponse>['onChange'] = (paginationConfig) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        // 移除排序功能
      },
      {
        title: 'LOGO',
        dataIndex: 'logo',
        key: 'logo',
        width: 80,
        render: (logo: string | null, record: FrameworkResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadLogo(record)}
          >
            <Avatar
              size={40}
              src={logo}
              icon={<FileImageOutlined />}
              shape="square"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
        render: (description: string | null) => description || '-',
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        width: 100,
        render: (active: number) => {
          const status = statusMap[active] || { name: '未知', color: 'default' };
          return <Tag color={status.color}>{status.name}</Tag>;
        },
      },
    ];

    // 排序模式下的操作列
    if (isSortMode) {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 80,
        render: (_: unknown, record: FrameworkResponse) => (
          <Space size="middle">
            <Tooltip title="排序模式下不可编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                disabled={true}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: FrameworkResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: FrameworkResponse) => (
          <Space size="middle">
            <Tooltip title="详情">
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '理论框架管理',
          },
        ]}
      />

      <Card
        title={
          isSortMode 
            ? "理论框架排序" 
            : (isRecycleBin ? "理论框架回收站" : "理论框架管理")
        }
        extra={
          <Space>
            {/* 排序模式下只显示保存和取消按钮 */}
            {isSortMode ? (
              <>
                <Tooltip title="保存排序">
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSortSave}
                    loading={loading}
                    disabled={!frameworks.length}
                  />
                </Tooltip>
                <Tooltip title="取消排序">
                  <Button
                    type="default"
                    icon={<CloseOutlined />}
                    onClick={handleSortCancel}
                    disabled={loading}
                  />
                </Tooltip>
              </>
            ) : (
              <>
                <Search
                  placeholder="搜索框架名称"
                  allowClear
                  onSearch={handleNameSearch}
                  style={{ width: 160 }}
                  ref={searchNameInputRef}
                  value={searchNameValue}
                  onChange={handleNameInputChange}
                />
                {/* 添加理论框架按钮只在非回收站模式下显示 */}
                {!isRecycleBin && selectedTenantId && (
                  <Tooltip title="添加理论框架">
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAdd}
                    />
                  </Tooltip>
                )}
                <Tooltip title="重置刷新">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                  />
                </Tooltip>
                {/* 排序按钮 - 只在非回收站模式下显示 */}
                {!isRecycleBin && selectedTenantId && (
                  <Tooltip title="调整排序">
                    <Button
                      type="default"
                      icon={<DragOutlined />}
                      onClick={enterSortMode}
                    />
                  </Tooltip>
                )}
                {/* 回收站/返回按钮 */}
                {isRecycleBin ? (
                  <Tooltip title="返回">
                    <Button
                      type="primary"
                      icon={<RollbackOutlined />}
                      onClick={toggleRecycleBin}
                    />
                  </Tooltip>
                ) : (
                  <Tooltip title="回收站">
                    <Button
                      type="default"
                      icon={<DeleteFilled />}
                      onClick={toggleRecycleBin}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </Space>
        }
      >
        {isSortMode ? (
          <SortableFrameworkTable
            frameworks={frameworks}
            loading={loading}
            onFrameworksChange={handleFrameworksChange}
            onEdit={handleEdit}
            onUploadLogo={handleUploadLogo}
          />
        ) : (
          <Table
            key={tableKey}
            columns={getColumns()}
            dataSource={frameworks}
            rowKey="id"
            loading={loading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            locale={{
              emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有理论框架' : '暂无理论框架数据') : '请先在顶部导航栏选择租户'
            }}
            onChange={handleTableChange}
            sortDirections={['ascend', 'descend', 'ascend']}
            showSorterTooltip={false}
          />
        )}
      </Card>

      {selectedTenantId && (
        <FrameworkForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          framework={currentFramework}
          tenantId={selectedTenantId}
        />
      )}

      {selectedTenantId && currentFramework && (
        <LogoUploader
          visible={logoUploaderVisible}
          onCancel={() => setLogoUploaderVisible(false)}
          onSuccess={handleLogoUploadSuccess}
          frameworkId={currentFramework.id}
          tenantId={selectedTenantId}
          currentLogo={currentFramework.logo || undefined}
        />
      )}
    </div>
  );
};

export default FrameworkManagement; 