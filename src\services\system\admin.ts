import { get, post, put, del } from '../api';

// 管理员信息接口 - 与API文档中的SysAdminResponse保持一致
export interface SysAdminResponse {
  id: number;
  username: string;
  name: string;
  user?: string; // 与API文档保持一致
  active: number; // 使用active而非is_active，与API文档保持一致
  role: number; // 角色: 0-超级管理员, 1-管理员
  ctime: string; // 使用ctime而非created_at
  utime?: string; // 更新时间
}

// 管理员列表响应接口
export interface SysAdminListResponse {
  total: number;
  items: SysAdminResponse[];
}

// 管理员创建接口 - 与API文档中的SysAdminCreate保持一致
export interface SysAdminCreate {
  username: string;
  password: string;
  name: string;
  user?: string;
  role: number; // 角色: 0-超级管理员, 1-管理员
}

// 管理员更新接口 - 与API文档中的SysAdminUpdate保持一致
export interface SysAdminUpdate {
  username?: string;
  password?: string;
  name?: string;
  user?: string;
  active?: number;
  role?: number; // 角色: 0-超级管理员, 1-管理员
}

// 查询参数接口 - 根据 openapi 文档完善
export interface AdminQueryParams {
  skip?: number;
  limit?: number;
  username?: string; // 按用户名搜索，支持模糊匹配
  name?: string; // 按姓名搜索，支持模糊匹配
  active?: number; // 按用户状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: string;
  sort_order?: string;
  start_time?: string;
  end_time?: string;
}

// 获取管理员列表
export const getAdmins = async (params: AdminQueryParams): Promise<SysAdminListResponse> => {
  return await get<SysAdminListResponse>('/sys/admin', params);
};

// 获取单个管理员
export const getAdmin = async (adminId: number): Promise<SysAdminResponse> => {
  return await get<SysAdminResponse>(`/sys/admin/${adminId}`);
};

// 创建管理员
export const createAdmin = async (admin: SysAdminCreate): Promise<SysAdminResponse> => {
  return await post<SysAdminResponse>('/sys/admin', admin);
};

// 更新管理员
export const updateAdmin = async (adminId: number, admin: SysAdminUpdate): Promise<SysAdminResponse> => {
  return await put<SysAdminResponse>(`/sys/admin/${adminId}`, admin);
};

// 删除管理员
export const deleteAdmin = async (adminId: number): Promise<void> => {
  return await del<void>(`/sys/admin/${adminId}`);
}; 