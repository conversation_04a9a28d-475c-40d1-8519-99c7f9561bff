import React, { useState, useRef, useEffect } from 'react';
import { Modal, Form, Input, Select } from 'antd';
import { 
  createTenantAdmin, 
  updateTenantAdmin,
  type TenantAdminResponse,
  type TenantAdminCreate,
  type TenantAdminUpdate 
} from '../../../services/system/tenant-admin';
import { getTenants, type TenantListResponse } from '../../../services/system/tenant';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { encryptPassword } from '../../../utils/crypto';

interface TenantAdminFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  admin: TenantAdminResponse | null;
}

const TenantAdminForm: React.FC<TenantAdminFormProps> = ({ visible, onCancel, onSuccess, admin }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<TenantListResponse['items']>([]);
  const [tenantsLoading, setTenantsLoading] = useState(false);
  
  // 防抖搜索的定时器引用
  const tenantSearchTimeoutRef = useRef<NodeJS.Timeout>();

  // 获取租户列表（支持搜索）
  const fetchTenants = async (searchKeyword?: string) => {
    setTenantsLoading(true);
    try {
      const response = await getTenants({
        skip: 0,
        limit: 100,
        keyword: searchKeyword,
      });
      setTenants(response.items);
      return response.items;
    } catch (error) {
      showError(error, '获取租户列表失败');
      return [];
    } finally {
      setTenantsLoading(false);
    }
  };

  // 处理租户搜索（防抖）
  const handleTenantSearch = (value: string) => {
    // 清除之前的定时器
    if (tenantSearchTimeoutRef.current) {
      clearTimeout(tenantSearchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表
    if (!value || value.trim() === '') {
      setTenants([]);
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    tenantSearchTimeoutRef.current = setTimeout(() => {
      fetchTenants(value.trim());
    }, 500);
  };

  useEffect(() => {
    if (visible) {
      if (admin) {
        form.setFieldsValue({
          tenant_id: admin.tenant_id,
          username: admin.username,
          name: admin.name,
          role: typeof admin.role === 'number' ? admin.role : 1,
        });
        
        // 编辑模式下，需要根据 tenant_id 获取租户信息并预加载到选项列表
        const loadCurrentTenant = async () => {
          try {
            const response = await getTenants({
              skip: 0,
              limit: 100,
            });
            const currentTenant = response.items.find(t => t.id === admin.tenant_id);
            if (currentTenant) {
              setTenants([currentTenant]);
            }
          } catch {
            // 如果获取失败，至少显示租户ID
            setTenants([{
              id: admin.tenant_id,
              name: `租户 ID: ${admin.tenant_id}`,
            } as TenantListResponse['items'][0]]);
          }
        };
        loadCurrentTenant();
      } else {
        form.resetFields();
        setTenants([]); // 新增模式下清空租户列表
      }
    }
  }, [visible, admin, form]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (tenantSearchTimeoutRef.current) {
        clearTimeout(tenantSearchTimeoutRef.current);
      }
    };
  }, []);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (admin) {
        const updateData: TenantAdminUpdate = {
          tenant_id: values.tenant_id,
          username: values.username,
          name: values.name,
          role: values.role,
        };
        
        if (values.password && values.password.trim() !== '') {
          updateData.password = encryptPassword(values.password);
        }
        
        await updateTenantAdmin(admin.id, updateData);
        showSuccess('更新租户管理员成功');
      } else {
        const createData: TenantAdminCreate = {
          tenant_id: values.tenant_id,
          username: values.username,
          password: encryptPassword(values.password),
          name: values.name,
          role: values.role,
        };
        await createTenantAdmin(createData);
        showSuccess('创建租户管理员成功');
      }

      form.resetFields();
      setTenants([]);
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setTenants([]);
    onCancel();
  };

  return (
    <Modal
      title={admin ? '编辑租户管理员' : '添加租户管理员'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ active: true, role: 1 }}
      >
        <Form.Item
          name="tenant_id"
          label="所属租户"
          rules={[{ required: true, message: '请选择租户' }]}
        >
          <Select
            placeholder="请输入租户名称进行搜索"
            allowClear
            showSearch
            loading={tenantsLoading}
            filterOption={false}
            onSearch={handleTenantSearch}
            notFoundContent={tenantsLoading ? '搜索中...' : tenants.length === 0 ? '请输入关键词搜索租户' : '暂无数据'}
          >
            {tenants.map(tenant => (
              <Select.Option key={tenant.id} value={tenant.id}>
                {tenant.name}（ID: {tenant.id}）
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          name="name"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>

        <Form.Item
          name="role"
          label="角色"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Select placeholder="请选择角色">
            <Select.Option value={1}>管理员</Select.Option>
            <Select.Option value={2}>运营人员</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: !admin, message: '请输入密码' }]}
        >
          <Input.Password placeholder={admin ? "留空则不修改密码" : "请输入密码"} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TenantAdminForm; 