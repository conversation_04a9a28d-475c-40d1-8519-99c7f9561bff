import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Select, Button, Space } from 'antd';
import { getBots, type Bot } from '../../../services/system/bot';
import { getTenants, type TenantListResponse } from '../../../services/system/tenant';
import { type TenantBotConfig } from '../../../services/system/tenant-bot-config';
import { showError } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface TenantBotConfigFormProps {
  editingConfig: TenantBotConfig | null;
  onSubmit: (values: { tenant_id: number; key: string; bid: number; notes?: string }) => Promise<void>;
  onCancel: () => void;
}

const TenantBotConfigForm: React.FC<TenantBotConfigFormProps> = ({
  editingConfig,
  onSubmit,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [bots, setBots] = useState<Bot[]>([]);
  const [botsLoading, setBotsLoading] = useState(false);
  const [tenants, setTenants] = useState<TenantListResponse['items']>([]);
  const [tenantsLoading, setTenantsLoading] = useState(false);
  
  // 防抖搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const tenantSearchTimeoutRef = useRef<NodeJS.Timeout>();

  // 获取租户列表（支持搜索）
  const fetchTenants = async (searchKeyword?: string) => {
    setTenantsLoading(true);
    try {
      const response = await getTenants({
        skip: 0,
        limit: 100,
        keyword: searchKeyword,
      });
      setTenants(response.items);
      return response.items;
    } catch (error) {
      showError(error, '获取租户列表失败');
      return [];
    } finally {
      setTenantsLoading(false);
    }
  };

  // 处理租户搜索（防抖）
  const handleTenantSearch = (value: string) => {
    // 清除之前的定时器
    if (tenantSearchTimeoutRef.current) {
      clearTimeout(tenantSearchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表
    if (!value || value.trim() === '') {
      setTenants([]);
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    tenantSearchTimeoutRef.current = setTimeout(() => {
      fetchTenants(value.trim());
    }, 500);
  };

  // 获取机器人列表（支持搜索）
  const fetchBots = async (searchKeyword?: string) => {
    setBotsLoading(true);
    try {
      const response = await getBots({ 
        skip: 0, 
        limit: 100,
        keyword: searchKeyword 
      });
      const activeBots = response.items.filter(bot => bot.active === 1);
      setBots(activeBots);
      return activeBots;
    } catch (error) {
      showError(error, '获取机器人列表失败');
      return [];
    } finally {
      setBotsLoading(false);
    }
  };

  // 处理机器人搜索（防抖）
  const handleBotSearch = (value: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表
    if (!value || value.trim() === '') {
      setBots([]);
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    searchTimeoutRef.current = setTimeout(() => {
      fetchBots(value.trim());
    }, 500);
  };

  // 初始化表单
  useEffect(() => {
    if (editingConfig) {
      form.setFieldsValue({
        tenant_id: editingConfig.tenant_id,
        key: editingConfig.key,
        bid: editingConfig.bid,
        notes: editingConfig.notes,
      });
      
      // 编辑模式下，需要根据 tenant_id 获取租户信息并预加载到选项列表
      const loadCurrentTenant = async () => {
        try {
          const response = await getTenants({
            skip: 0,
            limit: 100,
          });
          const currentTenant = response.items.find(t => t.id === editingConfig.tenant_id);
          if (currentTenant) {
            setTenants([currentTenant]);
          }
                 } catch {
           // 如果获取失败，至少显示租户ID
           setTenants([{
             id: editingConfig.tenant_id,
             name: `租户 ID: ${editingConfig.tenant_id}`,
           } as TenantListResponse['items'][0]]);
         }
      };
      loadCurrentTenant();
      
      if (editingConfig.bot_name) {
        setBots([{
          id: editingConfig.bid,
          name: editingConfig.bot_name,
          active: 1
        } as Bot]);
      }
    } else {
      form.resetFields();
      setTenants([]); // 新增模式下清空租户列表
      setBots([]); // 新增模式下清空机器人列表
    }
  }, [editingConfig, form]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (tenantSearchTimeoutRef.current) {
        clearTimeout(tenantSearchTimeoutRef.current);
      }
    };
  }, []);

  const handleCancel = () => {
    form.resetFields();
    setBots([]);
    setTenants([]);
    onCancel();
  };

  const handleSubmit = async (values: { tenant_id: number; key: string; bid: number; notes?: string }) => {
    await onSubmit(values);
    form.resetFields();
    setBots([]);
    setTenants([]);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      autoComplete="off"
    >
      <Form.Item
        name="tenant_id"
        label="所属租户"
        rules={[{ required: true, message: '请选择租户' }]}
      >
        <Select
          placeholder="请输入租户名称进行搜索"
          allowClear
          showSearch
          loading={tenantsLoading}
          filterOption={false}
          onSearch={handleTenantSearch}
          notFoundContent={tenantsLoading ? '搜索中...' : tenants.length === 0 ? '请输入关键词搜索租户' : '暂无数据'}
        >
          {tenants.map(tenant => (
            <Select.Option key={tenant.id} value={tenant.id}>
              {tenant.name}（ID: {tenant.id}）
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="key"
        label="Key"
        rules={[{ required: true, message: '请输入Key' }]}
      >
        <Input 
          placeholder="请输入Key" 
        />
      </Form.Item>

      <Form.Item 
        name="bid" 
        label="机器人选择"
        rules={[{ required: true, message: '请选择机器人' }]}
      >
        <Select
          placeholder="请输入机器人名称进行搜索"
          allowClear
          showSearch
          loading={botsLoading}
          filterOption={false}
          onSearch={handleBotSearch}
          notFoundContent={botsLoading ? '搜索中...' : bots.length === 0 ? '请输入关键词搜索机器人' : '暂无数据'}
        >
          {bots.map(bot => (
            <Select.Option key={bot.id} value={bot.id}>
              {bot.name}（ID: {bot.id}）
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="notes" label="备注">
        <TextArea rows={3} placeholder="请输入备注" />
      </Form.Item>

      <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
        <Space>
          <Button onClick={handleCancel}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            {editingConfig ? '更新' : '创建'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default TenantBotConfigForm; 