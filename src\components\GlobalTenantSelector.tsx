import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Select } from 'antd';
import { getTenants, type TenantListResponse } from '../services/system/tenant';
import { showError } from '../utils/errorHandler';

// 全局租户信息的 localStorage key
const GLOBAL_TENANT_STORAGE_KEY = 'globalTenant';

interface GlobalTenantSelectorProps {
  style?: React.CSSProperties;
  width?: number;
  placeholder?: string;
  disabled?: boolean;
}

export interface GlobalTenantInfo {
  id: number;
  name: string;
}

// 全局租户选择器组件
const GlobalTenantSelector: React.FC<GlobalTenantSelectorProps> = ({ 
  style, 
  width = 200, 
  placeholder = "选择租户",
  disabled = false
}) => {
  // 全局租户状态
  const [globalTenantId, setGlobalTenantId] = useState<number | undefined>(undefined);
  const [globalTenantName, setGlobalTenantName] = useState<string>('');
  const [tenants, setTenants] = useState<TenantListResponse['items']>([]);
  const [tenantsLoading, setTenantsLoading] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 从 localStorage 加载全局租户信息
  useEffect(() => {
    const savedTenant = localStorage.getItem(GLOBAL_TENANT_STORAGE_KEY);
    
    if (savedTenant) {
      try {
        const { id, name } = JSON.parse(savedTenant);
        setGlobalTenantId(id);
        setGlobalTenantName(name);
        // 如果有保存的租户，将其添加到选项中
        if (id && name) {
          setTenants([{ id, name } as TenantListResponse['items'][0]]);
        }
      } catch (error) {
        console.error('Failed to parse saved tenant:', error);
      }
    }
  }, []);

  // 保存全局租户信息到 localStorage
  const saveGlobalTenant = (id: number | undefined, name: string) => {
    if (id && name) {
      const tenantData = { id, name };
      localStorage.setItem(GLOBAL_TENANT_STORAGE_KEY, JSON.stringify(tenantData));
    } else {
      localStorage.removeItem(GLOBAL_TENANT_STORAGE_KEY);
    }
    
    // 触发自定义事件通知其他组件
    window.dispatchEvent(new CustomEvent('globalTenantChanged', {
      detail: { id, name }
    }));
  };

  // 获取租户列表（支持搜索）
  const fetchTenants = useCallback(async (searchKeyword?: string) => {
    setTenantsLoading(true);
    try {
      const response = await getTenants({
        skip: 0,
        limit: 100,
        keyword: searchKeyword,
      });
      setTenants(response.items);
      return response.items;
    } catch (error) {
      showError(error, '获取租户列表失败');
      return [];
    } finally {
      setTenantsLoading(false);
    }
  }, []);

  // 处理租户搜索（防抖）
  const handleTenantSearch = (value: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表但保留当前选中的租户
    if (!value || value.trim() === '') {
      if (globalTenantId && globalTenantName) {
        setTenants([{ id: globalTenantId, name: globalTenantName } as TenantListResponse['items'][0]]);
      } else {
        setTenants([]);
      }
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    searchTimeoutRef.current = setTimeout(() => {
      fetchTenants(value.trim());
    }, 500);
  };

  // 处理全局租户选择
  const handleGlobalTenantChange = (value: number | undefined, option?: { key: React.Key; value: number; children: React.ReactNode } | { key: React.Key; value: number; children: React.ReactNode }[]) => {
    if (value && option) {
      // 从选项中找到对应的租户信息
      const selectedTenant = tenants.find(tenant => tenant.id === value);
      const tenantName = selectedTenant ? selectedTenant.name : `租户${value}`;
      
      setGlobalTenantId(value);
      setGlobalTenantName(tenantName);
      saveGlobalTenant(value, tenantName);
    } else {
      setGlobalTenantId(undefined);
      setGlobalTenantName('');
      saveGlobalTenant(undefined, '');
      setTenants([]);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Select
      placeholder={placeholder}
      allowClear
      showSearch
      loading={tenantsLoading}
      filterOption={false}
      onSearch={handleTenantSearch}
      style={{ width, ...style }}
      value={globalTenantId}
      onChange={handleGlobalTenantChange}
      disabled={disabled}
      notFoundContent={tenantsLoading ? '搜索中...' : tenants.length === 0 ? '请输入租户名称' : '暂无数据'}
    >
      {tenants.map(tenant => (
        <Select.Option key={tenant.id} value={tenant.id}>
          {tenant.name}（ID: {tenant.id}）
        </Select.Option>
      ))}
    </Select>
  );
};

// 获取当前全局租户信息的工具函数
export const getGlobalTenantInfo = (): GlobalTenantInfo | null => {
  const savedTenant = localStorage.getItem(GLOBAL_TENANT_STORAGE_KEY);
  if (savedTenant) {
    try {
      const { id, name } = JSON.parse(savedTenant);
      return { id, name };
    } catch (error) {
      console.error('Failed to parse saved tenant:', error);
    }
  }
  return null;
};

// 清除全局租户信息的工具函数
export const clearGlobalTenantInfo = () => {
  localStorage.removeItem(GLOBAL_TENANT_STORAGE_KEY);
  window.dispatchEvent(new CustomEvent('globalTenantChanged', {
    detail: { id: undefined, name: '' }
  }));
};

export default GlobalTenantSelector; 