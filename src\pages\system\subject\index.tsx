import React, { useState, useEffect, useCallback } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, ExclamationCircleOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import { 
  getSubjects, 
  deleteSubject,
  type SubjectResponse
} from '../../../services/system/subject';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import SubjectForm from './SubjectForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;

const SubjectManagement: React.FC = () => {
  const [subjects, setSubjects] = useState<SubjectResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentSubject, setCurrentSubject] = useState<SubjectResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储名称输入框的当前值
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc'
  });

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取主题列表
  const fetchSubjects = useCallback(async () => {
    if (!selectedTenantId) {
      setSubjects([]);
      return;
    }

    try {
      setLoading(true);
      
      let sortBy: 'id' | 'ctime' | undefined;
      let sortOrder: 'asc' | 'desc' | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      }
      
      const response = await getSubjects(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      });
      
      // 手动过滤数据（因为接口不支持name查询）
      let filteredSubjects = [...response.items];
      
      // 手动实现名称搜索过滤
      if (searchName) {
        filteredSubjects = filteredSubjects.filter(subject => 
          subject.name.toLowerCase().includes(searchName.toLowerCase())
        );
      }
      
      setSubjects(filteredSubjects);
      setPagination(prev => ({
        ...prev,
        total: searchName ? filteredSubjects.length : response.total
      }));
    } catch (error) {
      showError(error, '获取主题列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, sortOrders, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchSubjects();
    }
  }, [fetchSubjects]);

  // 处理名称搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理名称搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理添加主题
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentSubject(null);
    setModalVisible(true);
  };

  // 处理编辑主题
  const handleEdit = (record: SubjectResponse) => {
    setCurrentSubject(record);
    setModalVisible(true);
  };

  // 处理删除主题
  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个主题吗？删除后无法恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteSubject(id);
          showSuccess('删除成功');
          fetchSubjects();
        } catch (error: unknown) {
          showError(error, '删除主题失败');
        }
      },
    });
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchSubjects();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc'
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));
    
    // 数据会通过useEffect自动重新加载
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<SubjectResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      sorter: true,
      sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
    },
    {
      title: '主题名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: unknown, record: SubjectResponse) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="primary"
              style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '主题管理',
          },
        ]}
      />

      <Card
        title="主题管理"
        extra={
          <Space>
            <Search
              placeholder="搜索主题名称"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            {selectedTenantId && (
              <Tooltip title="添加主题">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={subjects}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? '暂无主题数据' : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <SubjectForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          subject={currentSubject}
          tenantId={selectedTenantId}
        />
      )}
    </div>
  );
};

export default SubjectManagement; 