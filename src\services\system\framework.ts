import { get, post, put, del } from '../api';

// 理论框架响应数据类型
export interface FrameworkResponse {
  id: number;
  tenant_id: number;
  name: string;
  description?: string | null;
  logo?: string | null;
  priority: number;
  active: number;
}

// 创建理论框架数据类型
export interface FrameworkCreate {
  tenant_id: number;
  name: string;
  description?: string;
  priority?: number;
  active?: number;
}

// 更新理论框架数据类型
export interface FrameworkUpdate {
  name?: string;
  description?: string;
  logo?: string;
  priority?: number;
  active?: number;
}

// 分页响应类型
export interface FrameworkListResponse {
  items: FrameworkResponse[];
  total: number;
}

// 查询参数类型
export interface FrameworkQuery {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number;
  sort_by?: 'id' | 'priority';
  sort_order?: 'asc' | 'desc';
}

// LOGO上传URL响应类型
export interface LogoUploadUrlResponse {
  upload_url: string;
  file_path: string;
  file_url?: string;
  expires?: number;
}

// LOGO上传URL请求类型
export interface LogoUploadUrlRequest {
  tenant_id: number;
  file_name: string;
}

// 获取理论框架列表
export const getFrameworks = async (tenantId: number, params?: Omit<FrameworkQuery, 'tenant_id'>): Promise<FrameworkListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<FrameworkListResponse>('/sys/framework', queryParams);
};

// 获取单个理论框架
export const getFramework = async (id: number): Promise<FrameworkResponse> => {
  return await get<FrameworkResponse>(`/sys/framework/${id}`);
};

// 创建理论框架
export const createFramework = async (data: FrameworkCreate): Promise<FrameworkResponse> => {
  return await post<FrameworkResponse>('/sys/framework', data);
};

// 更新理论框架
export const updateFramework = async (id: number, data: FrameworkUpdate): Promise<FrameworkResponse> => {
  return await put<FrameworkResponse>(`/sys/framework/${id}`, data);
};

// 删除理论框架
export const deleteFramework = async (id: number): Promise<void> => {
  await del<void>(`/sys/framework/${id}`);
};

// 获取LOGO上传URL
export const getFrameworkLogoUploadUrl = async (params: LogoUploadUrlRequest): Promise<LogoUploadUrlResponse> => {
  return await get<LogoUploadUrlResponse>('/sys/framework/logo/upload-url', params);
};

// 更新理论框架LOGO
export const updateFrameworkLogo = async (frameworkId: number, logoPath: string): Promise<FrameworkResponse> => {
  if (frameworkId === undefined || logoPath === undefined || logoPath === null || logoPath === '') {
    throw new Error('Invalid parameters for updating framework logo');
  }

  const requestBody = { logo: logoPath };
  return await put<FrameworkResponse>(`/sys/framework/${frameworkId}`, requestBody);
};

// 删除LOGO文件
export const deleteFrameworkLogoFile = async (filePath: string): Promise<void> => {
  if (!filePath) {
    throw new Error('File path is required for deleting logo file');
  }

  return await del<void>('/sys/framework/logo', { 
    data: { file_path: filePath } 
  });
};

// 批量更新框架顺序相关类型
export interface FrameworkOrderItem {
  id: number;
  priority: number;
}

export interface FrameworkBatchOrderRequest {
  tenant_id: number;
  frameworks: FrameworkOrderItem[];
}

export interface FrameworkBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_frameworks: FrameworkResponse[];
}

// 批量更新框架顺序
export const batchUpdateFrameworkOrder = async (data: FrameworkBatchOrderRequest): Promise<FrameworkBatchOrderResponse> => {
  return await put<FrameworkBatchOrderResponse>('/sys/framework/batch/order', data);
}; 