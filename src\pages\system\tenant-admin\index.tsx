import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, DatePicker, Tag, Select, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import dayjs, { type Dayjs } from 'dayjs';
import { 
  getTenantAdmins, 
  deleteTenantAdmin,
  updateTenantAdmin,
  type TenantAdminResponse 
} from '../../../services/system/tenant-admin';
import { getTenants, type TenantListResponse } from '../../../services/system/tenant';
import { showError, showSuccess } from '../../../utils/errorHandler';
import TenantAdminForm from './TenantAdminForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

// 角色映射
const roleMap: Record<number, { name: string; color: string }> = {
  1: { name: '管理员', color: 'blue' },
  2: { name: '运营人员', color: 'green' },
};

const TenantAdminManagement: React.FC = () => {
  const [admins, setAdmins] = useState<TenantAdminResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentAdmin, setCurrentAdmin] = useState<TenantAdminResponse | null>(null);
  const [searchUsername, setSearchUsername] = useState('');
  const [searchUsernameValue, setSearchUsernameValue] = useState('');
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState('');
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  const [tenants, setTenants] = useState<TenantListResponse['items']>([]);
  const [tenantsLoading, setTenantsLoading] = useState(false);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  const isFirstRender = useRef(true);
  // 防抖搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 获取租户列表（支持搜索）
  const fetchTenants = useCallback(async (searchKeyword?: string) => {
    setTenantsLoading(true);
    try {
      const response = await getTenants({
        skip: 0,
        limit: 100,
        keyword: searchKeyword,
      });
      setTenants(response.items);
      return response.items;
    } catch (error) {
      showError(error, '获取租户列表失败');
      return [];
    } finally {
      setTenantsLoading(false);
    }
  }, []);

  // 处理租户搜索（防抖）
  const handleTenantSearch = (value: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表
    if (!value || value.trim() === '') {
      setTenants([]);
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    searchTimeoutRef.current = setTimeout(() => {
      fetchTenants(value.trim());
    }, 500);
  };

  // 获取租户管理员列表
  const fetchAdmins = useCallback(async () => {
    try {
      setLoading(true);
      
      let sortBy: string | undefined;
      let sortOrder: string | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getTenantAdmins({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        username: searchUsername || undefined,
        name: searchName || undefined,
        tenant_id: selectedTenantId,
        active: isRecycleBin ? 0 : 1,
        sort_by: sortBy,
        sort_order: sortOrder,
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
      });
      
      setAdmins(response.items);
      
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取租户管理员列表失败');
    } finally {
      setLoading(false);
    }
  }, [searchUsername, searchName, selectedTenantId, sortOrders, isRecycleBin, dateRange, pagination.current, pagination.pageSize]);

  useEffect(() => {
    fetchAdmins();
  }, []);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchAdmins();
  }, [isRecycleBin, searchUsername, searchName, selectedTenantId, dateRange, sortOrders]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // 处理用户名搜索
  const handleUsernameSearch = (value: string) => {
    setSearchUsername(value);
    setSearchUsernameValue(value);
  };

  const handleUsernameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchUsernameValue(e.target.value);
  };

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理租户筛选
  const handleTenantChange = (value: number | undefined) => {
    setSelectedTenantId(value);
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: [Dayjs | null, Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
  };

  // 处理添加管理员
  const handleAdd = () => {
    setCurrentAdmin(null);
    setModalVisible(true);
  };

  // 处理编辑管理员
  const handleEdit = (record: TenantAdminResponse) => {
    setCurrentAdmin(record);
    setModalVisible(true);
  };

  // 处理禁用/删除管理员
  const handleDisable = (id: number) => {
    const actionText = isRecycleBin ? '彻底删除' : '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个租户管理员吗？${isRecycleBin ? '此操作不可撤销。' : '禁用后可在回收站中恢复。'}`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (isRecycleBin) {
            await deleteTenantAdmin(id);
          } else {
            await updateTenantAdmin(id, { active: 0 });
          }
          showSuccess(`${actionText}成功`);
          fetchAdmins();
        } catch (error: unknown) {
          showError(error, `${actionText}租户管理员失败`);
        }
      },
    });
  };

  // 处理恢复管理员
  const handleRestore = async (adminId: number) => {
    try {
      await updateTenantAdmin(adminId, { active: 1 });
      showSuccess('恢复成功');
      fetchAdmins();
    } catch (error) {
      showError(error, '恢复租户管理员失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchAdmins();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    isFirstRender.current = true;

    setSearchUsername('');
    setSearchUsernameValue('');
    setSearchName('');
    setSearchNameValue('');
    setSelectedTenantId(undefined);
    setTenants([]); // 清空租户列表
    setDateRange(null);

    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    try {
      setLoading(true);
      const response = await getTenantAdmins({
        skip: 0,
        limit: 10,
        username: undefined,
        name: undefined,
        tenant_id: undefined,
        active: isRecycleBin ? 0 : 1, // 根据当前模式传递正确的active参数进行服务端过滤
        sort_by: 'id',
        sort_order: 'desc',
        start_time: undefined,
        end_time: undefined,
      });
      
      // 直接使用API返回的数据，无需客户端过滤
      setAdmins(response.items);
      
      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
      
      setTableKey(prevKey => prevKey + 1);
    } catch (error) {
      showError(error, '获取租户管理员列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<TenantAdminResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };



  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '所属租户',
        dataIndex: 'tenant_name',
        key: 'tenant_name',
        render: (tenantName: string, record: TenantAdminResponse) => 
          tenantName || `ID: ${record.tenant_id}`,
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username',
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        render: (role: number) => {
          const roleInfo = roleMap[role] || { name: '未知角色', color: 'default' };
          return <Tag color={roleInfo.color}>{roleInfo.name}</Tag>;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        render: (text: string) => new Date(text).toLocaleString(),
        sorter: true,
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : undefined,
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        render: (active: number) => (
          <span style={{ color: active === 1 ? 'green' : 'red' }}>
            {active === 1 ? '启用' : '禁用'}
          </span>
        ),
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: TenantAdminResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: TenantAdminResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '租户管理员管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "租户管理员回收站" : "租户管理员管理"}
        extra={
          <Space>
            <Select
              placeholder="选择租户"
              allowClear
              showSearch
              loading={tenantsLoading}
              filterOption={false}
              onSearch={handleTenantSearch}
              style={{ width: 160 }}
              value={selectedTenantId}
              onChange={handleTenantChange}
              notFoundContent={tenantsLoading ? '搜索中...' : tenants.length === 0 ? '请输入租户名称' : '暂无数据'}
            >
              {tenants.map(tenant => (
                <Select.Option key={tenant.id} value={tenant.id}>
                  {tenant.name}（ID: {tenant.id}）
                </Select.Option>
              ))}
            </Select>
            <Search
              placeholder="搜索用户名"
              allowClear
              onSearch={handleUsernameSearch}
              style={{ width: 160 }}
              value={searchUsernameValue}
              onChange={handleUsernameInputChange}
            />
            <Search
              placeholder="搜索姓名"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {!isRecycleBin && (
              <Tooltip title="添加租户管理员">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={admins}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: isRecycleBin ? '回收站中没有租户管理员' : '暂无数据'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      <TenantAdminForm
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleFormSuccess}
        admin={currentAdmin}
      />
    </div>
  );
};

export default TenantAdminManagement; 