import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Popconfirm,
  Card,
  Tooltip,
  Breadcrumb,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getBotConfigs,
  createBotConfig,
  updateBotConfig,
  deleteBotConfig,
  type BotConfig,
  type BotConfigCreate,
  type BotConfigUpdate,
} from '../../../services/system/bot-config';
import { showError, showSuccess } from '../../../utils/errorHandler';
import BotConfigForm from './BotConfigForm';
import { Link } from 'react-router-dom';

const { Search } = Input;

const BotConfigManagement: React.FC = () => {
  const [configs, setConfigs] = useState<BotConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [keywordValue, setKeywordValue] = useState(''); // 用于存储输入框的当前值
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<BotConfig | null>(null);

  // 获取设置列表
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await getBotConfigs({
        skip: (current - 1) * pageSize,
        limit: pageSize,
        keyword: keyword || undefined,
      });
      setConfigs(response.items);
      setTotal(response.total);
    } catch (error) {
      showError(error, '获取设置列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, [current, pageSize, keyword]);

  // 搜索设置
  const handleSearch = (value: string) => {
    setKeyword(value);
    setKeywordValue(value);
    setCurrent(1);
  };

  // 处理搜索输入框值变化，但不触发搜索
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeywordValue(e.target.value);
  };

  // 新增设置
  const handleAdd = () => {
    setEditingConfig(null);
    setIsModalVisible(true);
  };

  // 编辑设置
  const handleEdit = (config: BotConfig) => {
    setEditingConfig(config);
    setIsModalVisible(true);
  };

  // 删除设置
  const handleDelete = async (key: string) => {
    try {
      await deleteBotConfig(key);
      showSuccess('删除成功');
      fetchConfigs();
    } catch (error) {
      showError(error, '删除失败');
    }
  };

  // 处理重置刷新按钮点击
  const handleRefresh = async () => {
    // 清空搜索条件
    setKeyword('');
    setKeywordValue('');
    
    // 重置分页状态
    setCurrent(1);
    setPageSize(10);

    try {
      setLoading(true);
      const response = await getBotConfigs({
        skip: 0,
        limit: 10,
        keyword: undefined,
      });
      setConfigs(response.items);
      setTotal(response.total);
    } catch (error) {
      showError(error, '刷新数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: {
    key: string;
    bid: number;
    notes?: string;
  }) => {
    try {
      if (editingConfig) {
        // 编辑设置 - 现在可以修改key、bid和notes
        const updateData: BotConfigUpdate = {
          key: values.key,
          bid: values.bid,
          notes: values.notes,
        };
        await updateBotConfig(editingConfig.key, updateData);
        showSuccess('更新成功');
      } else {
        // 新增设置
        const createData: BotConfigCreate = {
          key: values.key,
          bid: values.bid,
          notes: values.notes,
        };
        await createBotConfig(createData);
        showSuccess('创建成功');
      }

      setIsModalVisible(false);
      fetchConfigs();
    } catch (error) {
      showError(error, editingConfig ? '更新失败' : '创建失败');
    }
  };

  // 根据机器人ID获取机器人名称（简化版本）
  const getBotName = (config: BotConfig) => {
    // 直接返回机器人名称，因为 bot_name 是必需字段
    return config.bot_name;
  };

  const columns: ColumnsType<BotConfig> = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      width: 200,
    },
    {
      title: '机器人名称',
      dataIndex: 'bid',
      key: 'botName',
      width: 150,
      render: (bid: number, record: BotConfig) => getBotName(record),
    },
    {
      title: '机器人ID',
      dataIndex: 'bid',
      key: 'bid',
      width: 100,
      render: (bid: number) => bid,
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个设置吗？"
            onConfirm={() => handleDelete(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '全局机器人设置管理',
          },
        ]}
      />

      <Card
        title="全局机器人设置管理"
        extra={
          <Space>
            <Search
              placeholder="搜索Key"
              allowClear
              value={keywordValue}
              onChange={handleSearchInputChange}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
            
            <Tooltip title="添加全局机器人设置">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              />
            </Tooltip>
            
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="key"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
          locale={{
            emptyText: '暂无数据'
          }}
        />
      </Card>

      <Modal
        title={editingConfig ? '编辑全局机器人设置' : '添加全局机器人设置'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <BotConfigForm
          editingConfig={editingConfig}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalVisible(false)}
        />
      </Modal>
    </div>
  );
};

export default BotConfigManagement; 