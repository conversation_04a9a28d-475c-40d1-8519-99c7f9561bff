import { get, post, put, del } from '../api';

// 主题响应数据类型
export interface SubjectResponse {
  id: number;
  tenant_id: number;
  name: string;
}

// 创建主题数据类型
export interface SubjectCreate {
  tenant_id: number;
  name: string;
}

// 更新主题数据类型
export interface SubjectUpdate {
  name?: string;
}

// 分页响应类型
export interface SubjectListResponse {
  items: SubjectResponse[];
  total: number;
}

// 查询参数类型
export interface SubjectQuery {
  tenant_id: number;
  skip?: number;
  limit?: number;
  name?: string;  // 按名称搜索
  // active?: number; // 主题暂时可能没有active字段，保留注释待确认
  sort_by?: 'id' | 'ctime';
  sort_order?: 'asc' | 'desc';
}

// 获取主题列表
export const getSubjects = async (tenantId: number, params?: Omit<SubjectQuery, 'tenant_id'>): Promise<SubjectListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<SubjectListResponse>('/sys/subject', queryParams);
};

// 获取单个主题
export const getSubject = async (id: number): Promise<SubjectResponse> => {
  return await get<SubjectResponse>(`/sys/subject/${id}`);
};

// 创建主题
export const createSubject = async (data: SubjectCreate): Promise<SubjectResponse> => {
  return await post<SubjectResponse>('/sys/subject', data);
};

// 更新主题
export const updateSubject = async (id: number, data: SubjectUpdate): Promise<SubjectResponse> => {
  return await put<SubjectResponse>(`/sys/subject/${id}`, data);
};

// 删除主题
export const deleteSubject = async (id: number): Promise<void> => {
  await del<void>(`/sys/subject/${id}`);
};

// 搜索主题（用于动态补全）
export const searchSubjects = async (tenantId: number, name: string): Promise<SubjectResponse[]> => {
  const response = await getSubjects(tenantId, {
    name,
    limit: 50, // 限制搜索结果数量
  });
  return response.items;
}; 