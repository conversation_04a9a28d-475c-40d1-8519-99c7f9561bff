// 学员管理相关API接口
import { get, post, put } from '../api';

// 学员响应类型
export interface StudentResponse {
  id: number;
  tenant_id: number;
  uid: number;
  username: string;
  name: string;
  gender: number;
  notes?: string;
  active: number;
  ctime: string;
}

// 创建学员请求类型（根据openapi.json定义）
export interface StudentCreate {
  tenant_id: number;
  username: string;
  password: string;
  name: string;
  gender?: number;
  notes?: string;
}

// 更新学员请求类型
export interface StudentUpdate {
  name?: string;
  gender?: number;
  notes?: string;
  active?: number;
  password?: string;
}

// 获取学员列表请求参数（根据openapi.json定义）
export interface StudentListParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number; // 按用户状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: 'id' | 'ctime';
  sort_order?: 'asc' | 'desc';
  start_time?: string; // 创建时间的开始时间，格式为ISO 8601
  end_time?: string; // 创建时间的结束时间，格式为ISO 8601
}

// 学员列表响应类型（根据openapi.json定义）
export interface StudentListResponse {
  items: StudentResponse[];
  total: number;
}

// 获取学员列表 - 更新为匹配 GET /api/v1/sys/student
export const getStudents = async (tenantId: number, params?: Omit<StudentListParams, 'tenant_id'>): Promise<StudentListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<StudentListResponse>('/sys/student', queryParams);
};

// 获取单个学员 - 更新为匹配 GET /api/v1/sys/student/{student_id}
export const getStudent = async (studentId: number): Promise<StudentResponse> => {
  return await get<StudentResponse>(`/sys/student/${studentId}`);
};

// 创建学员 - 更新为匹配 POST /api/v1/sys/student
export const createStudent = async (data: StudentCreate): Promise<StudentResponse> => {
  return await post<StudentResponse>('/sys/student', data);
};

// 更新学员 - 更新为匹配 PUT /api/v1/sys/student/{student_id}
export const updateStudent = async (studentId: number, data: StudentUpdate): Promise<StudentResponse> => {
  return await put<StudentResponse>(`/sys/student/${studentId}`, data);
};

// 删除学员（软删除，通过设置active为0实现）
export const deleteStudent = async (studentId: number): Promise<StudentResponse> => {
  return await updateStudent(studentId, { active: 0 });
}; 