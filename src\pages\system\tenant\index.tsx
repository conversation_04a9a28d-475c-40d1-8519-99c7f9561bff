import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, DatePicker, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { 
  getTenants, 
  deleteTenant,
  updateTenant,
  type Tenant 
} from '../../../services/system/tenant';
import { showError, showSuccess } from '../../../utils/errorHandler';
import TenantForm from './TenantForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const TenantManagement: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchKeywordValue, setSearchKeywordValue] = useState('');
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  const isFirstRender = useRef(true);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const datePickerRef = useRef<any>(null);

  const fetchTenants = useCallback(async () => {
    try {
      setLoading(true);
      
      let sortBy: string | undefined;
      let sortOrder: string | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getTenants({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        keyword: searchKeyword || undefined,
        active: isRecycleBin ? 0 : 1,
        sort_by: sortBy,
        sort_order: sortOrder,
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
      });
      
      setTenants(response.items);
      
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取租户列表失败');
    } finally {
      setLoading(false);
    }
  }, [searchKeyword, sortOrders, isRecycleBin, dateRange, pagination.current, pagination.pageSize]);

  useEffect(() => {
    fetchTenants();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    fetchTenants();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRecycleBin, searchKeyword, dateRange, sortOrders]);

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setSearchKeywordValue(value);
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeywordValue(e.target.value);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
  };

  const handleAdd = () => {
    setCurrentTenant(null);
    setModalVisible(true);
  };

  const handleEdit = (record: Tenant) => {
    setCurrentTenant(record);
    setModalVisible(true);
  };

  const handleDisable = (id: number) => {
    const actionText = isRecycleBin ? '彻底删除' : '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个租户吗？${isRecycleBin ? '此操作不可撤销。' : '禁用后可在回收站中恢复。'}`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (isRecycleBin) {
            await deleteTenant(id);
          } else {
            await updateTenant(id, { active: 0 });
          }
          showSuccess(`${actionText}成功`);
          fetchTenants();
        } catch (error: unknown) {
          showError(error, `${actionText}租户失败`);
        }
      },
    });
  };

  const handleRestore = async (tenantId: number) => {
    try {
      await updateTenant(tenantId, { active: 1 });
      showSuccess('恢复成功');
      fetchTenants();
    } catch (error) {
      showError(error, '恢复租户失败');
    }
  };

  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchTenants();
  };

  const handleRefresh = async () => {
    isFirstRender.current = true;

    setSearchKeyword('');
    setSearchKeywordValue('');

    setDateRange(null);

    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    try {
      setLoading(true);
      const response = await getTenants({
        skip: 0,
        limit: 10,
        keyword: undefined,
        active: isRecycleBin ? 0 : 1, // 根据当前模式传递正确的active参数进行服务端过滤
        sort_by: 'id',
        sort_order: 'desc',
        start_time: undefined,
        end_time: undefined,
      });
      
      setTenants(response.items);
      
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      setTableKey(prev => prev + 1);

      setTimeout(() => {
        isFirstRender.current = false;
      }, 0);

    } catch (error) {
      showError(error, '刷新数据失败');
    } finally {
      setLoading(false);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableChange = (paginationConfig: any, _filters: Record<string, unknown>, sorter: unknown) => {
    setPagination(prev => ({
      ...prev,
      current: paginationConfig.current || 1,
      pageSize: paginationConfig.pageSize || 10,
    }));

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '租户代号',
        dataIndex: 'code',
        key: 'code',
        width: 120,
      },
      {
        title: '租户名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        ellipsis: true,
        render: (text: string) => text || '-',
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        width: 180,
        sorter: true,
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : undefined,
        render: (text: string) => {
          if (!text) return '-';
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        width: 80,
        render: (active: number) => (
          <span style={{ color: active === 1 ? 'green' : 'red' }}>
            {active === 1 ? '启用' : '禁用'}
          </span>
        ),
      },
    ];

    if (isRecycleBin) {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: Tenant) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<RollbackOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: Tenant) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '租户管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "租户回收站" : "租户管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索租户名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: 160 }}
              ref={searchInputRef}
              value={searchKeywordValue}
              onChange={handleSearchInputChange}
            />
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              ref={datePickerRef}
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {!isRecycleBin && (
              <Tooltip title="添加租户">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={tenants}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: isRecycleBin ? '回收站中没有租户' : '暂无数据'
          }}
          onChange={handleTableChange}
          scroll={{ x: 'max-content' }}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      <TenantForm
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleFormSuccess}
        tenant={currentTenant}
      />
    </div>
  );
};

export default TenantManagement; 