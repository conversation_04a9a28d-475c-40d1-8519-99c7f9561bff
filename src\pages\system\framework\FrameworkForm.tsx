import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createFramework, 
  updateFramework,
  type FrameworkResponse,
  type FrameworkCreate,
  type FrameworkUpdate
} from '../../../services/system/framework';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface FrameworkFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  framework: FrameworkResponse | null;
  tenantId: number;
}

const FrameworkForm: React.FC<FrameworkFormProps> = ({ visible, onCancel, onSuccess, framework, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (framework) {
        form.setFieldsValue({
          name: framework.name,
          description: framework.description,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, framework, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (framework) {
        const updateData: FrameworkUpdate = {
          name: values.name,
          description: values.description,
        };
        
        await updateFramework(framework.id, updateData);
        showSuccess('更新理论框架成功');
      } else {
        const createData: FrameworkCreate = {
          tenant_id: tenantId,
          name: values.name,
          description: values.description,
        };
        await createFramework(createData);
        showSuccess('创建理论框架成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={framework ? '编辑理论框架' : '添加理论框架'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="框架名称"
          name="name"
          rules={[
            { required: true, message: '请输入框架名称' },
            { max: 100, message: '框架名称不能超过100个字符' }
          ]}
        >
          <Input placeholder="请输入框架名称" />
        </Form.Item>

        <Form.Item
          label="描述"
          name="description"
          rules={[
            { max: 500, message: '描述不能超过500个字符' }
          ]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入框架描述" 
            showCount 
            maxLength={500}
          />
        </Form.Item>


      </Form>
    </Modal>
  );
};

export default FrameworkForm; 