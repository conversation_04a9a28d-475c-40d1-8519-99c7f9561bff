import React, { useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import {
  createModule,
  updateModule,
  type ModuleCreate,
  type ModuleUpdate,
  type ModuleResponse,
} from '../../../services/system/module';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface ModuleFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  module?: ModuleResponse | null;
  tenantId: number;
  frameworkId?: number;
}

const ModuleForm: React.FC<ModuleFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  module,
  tenantId,
  frameworkId,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      if (module) {
        // 编辑模式：设置表单值
        form.setFieldsValue({
          name: module.name,
          description: module.description || '',
        });
      } else {
        // 新建模式：重置表单
        form.resetFields();
      }
    }
  }, [visible, module, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (module) {
        // 编辑模式
        const updateData: ModuleUpdate = {
          name: values.name,
          description: values.description || undefined,
        };
        
        // 如果有框架ID，则设置
        if (frameworkId) {
          updateData.fid = frameworkId;
        }
        
        await updateModule(module.id, updateData);
        showSuccess('更新成功');
      } else {
        // 新建模式
        const createData: ModuleCreate = {
          tenant_id: tenantId,
          name: values.name,
          description: values.description || undefined,
          active: 1, // 默认启用状态
        };
        
        // 如果有框架ID，则设置
        if (frameworkId) {
          createData.fid = frameworkId;
        }
        
        await createModule(createData);
        showSuccess('创建成功');
      }
      
      onSuccess();
    } catch (error) {
      showError(error, module ? '更新失败' : '创建失败');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={module ? '编辑模块' : '新建模块'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          label="模块名称"
          name="name"
          rules={[
            { required: true, message: '请输入模块名称' },
            { max: 100, message: '模块名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入模块名称" />
        </Form.Item>

        <Form.Item
          label="模块描述"
          name="description"
          rules={[
            { max: 500, message: '描述不能超过500个字符' },
          ]}
        >
          <TextArea 
            placeholder="请输入模块描述" 
            rows={3}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ModuleForm; 