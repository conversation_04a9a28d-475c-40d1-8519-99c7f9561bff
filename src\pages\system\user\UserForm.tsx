import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createSystemUser, 
  updateSystemUser,
  type SystemUser,
  type SystemUserCreate,
  type SystemUserUpdate 
} from '../../../services/system/user';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { encryptPassword } from '../../../utils/crypto';

interface UserFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  user: SystemUser | null;
}

const UserForm: React.FC<UserFormProps> = ({ visible, onCancel, onSuccess, user }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (user) {
        form.setFieldsValue({
          username: user.username,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, user, form]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (user) {
        const updateData: SystemUserUpdate = {
          username: values.username,
        };
        
        if (values.password && values.password.trim() !== '') {
          updateData.password = encryptPassword(values.password);
        }
        
        await updateSystemUser(user.id, updateData);
        showSuccess('更新用户成功');
      } else {
        const createData: SystemUserCreate = {
          username: values.username,
          password: encryptPassword(values.password),
          name: values.username,
          active: 1,
        };
        await createSystemUser(createData);
        showSuccess('创建用户成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={user ? '编辑用户' : '添加用户'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ active: true }}
      >
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>



        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: !user, message: '请输入密码' }]}
        >
          <Input.Password placeholder={user ? "留空则不修改密码" : "请输入密码"} />
        </Form.Item>


      </Form>
    </Modal>
  );
};

export default UserForm; 