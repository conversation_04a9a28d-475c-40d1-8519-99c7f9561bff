import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Tag, Breadcrumb, message } from 'antd';
import { PlusOutlined, EditOutlined, ReloadOutlined, ExclamationCircleOutlined, HomeOutlined, DragOutlined, SaveOutlined, CloseOutlined, StopOutlined, UndoOutlined, DeleteFilled, RollbackOutlined } from '@ant-design/icons';
import { useSearchParams, Link } from 'react-router-dom';
import type { TableProps } from 'antd/es/table';
import {
  getModules,
  updateModule,
  batchUpdateModuleOrder,
  type ModuleResponse,
  type ModuleQuery,
} from '../../../services/system/module';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import ModuleForm from './ModuleForm';
import SortableModuleTable from './SortableModuleTable';

const { Search } = Input;
const { confirm } = Modal;

const ModuleManagement: React.FC = () => {
  const [modules, setModules] = useState<ModuleResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [keywordValue, setKeywordValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingModule, setEditingModule] = useState<ModuleResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [tableKey, setTableKey] = useState<number>(0);
  
  // 排序模式相关状态
  const [isSortMode, setIsSortMode] = useState(false);
  const [originalModules, setOriginalModules] = useState<ModuleResponse[]>([]);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchKeywordInputRef = useRef<any>(null);
  
  // 获取URL参数
  const [searchParams] = useSearchParams();
  const frameworkId = searchParams.get('framework_id');
  const frameworkName = searchParams.get('framework_name') || '理论框架';

  // 定义状态映射
  const statusMap: { [key: number]: { name: string; color: string } } = {
    0: { name: '禁用', color: 'red' },
    1: { name: '启用', color: 'green' },
  };

  // 进入排序模式
  const enterSortMode = () => {
    if (isRecycleBin) {
      message.warning('回收站模式下不能进行排序');
      return;
    }
    if (!selectedTenantId) {
      message.warning('请先选择租户');
      return;
    }
    
    // 保存原始数据用于取消时恢复
    setOriginalModules([...modules]);
    setIsSortMode(true);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 退出排序模式
  const exitSortMode = () => {
    setIsSortMode(false);
    setOriginalModules([]);
  };

  // 保存排序
  const handleSortSave = async () => {
    if (loading) return; // 防止重复提交
    
    try {
      setLoading(true);
      
      // 检查是否有实际变化
      const hasChanges = modules.some((module, index) => {
        const originalIndex = originalModules.findIndex(orig => orig.id === module.id);
        return originalIndex !== index;
      });
      
      if (!hasChanges) {
        message.info('排序未发生变化');
        exitSortMode();
        return;
      }
      
      // 准备批量更新数据
      const moduleUpdates = modules.map((module, index) => ({
        id: module.id,
        priority: index + 1 // 优先级从小到大，第一个项目priority=1，排在最前面
      }));
      
      // 调用批量更新接口
      const response = await batchUpdateModuleOrder({
        tenant_id: selectedTenantId!,
        modules: moduleUpdates
      });
      
      showSuccess(`排序保存成功，共更新 ${response.success_count} 个项目`);
      
      // 重新获取数据
      await fetchModules();
      exitSortMode();
    } catch (error) {
      showError(error, '保存排序失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消排序的回调
  const handleSortCancel = () => {
    setModules([...originalModules]);
    exitSortMode();
  };

  // 模块数据变化的回调
  const handleModulesChange = (newModules: ModuleResponse[]) => {
    setModules(newModules);
  };

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取模块列表
  const fetchModules = useCallback(async () => {
    if (!selectedTenantId) {
      setModules([]);
      return;
    }

    try {
      setLoading(true);
      
      // 始终按照 priority 从小到大排序
      const sortBy = 'priority';
      const sortOrder = 'asc';
      
      const params: ModuleQuery = {
        tenant_id: selectedTenantId,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
      };

      // 如果有框架ID参数，则筛选该框架下的模块
      if (frameworkId) {
        params.framework_id = parseInt(frameworkId);
      }

      const response = await getModules(params);
      
      // 手动过滤数据（因为接口不支持name查询）
      let filteredModules = [...response.items];
      
      // 手动实现名称搜索过滤
      if (keyword) {
        filteredModules = filteredModules.filter(module => 
          module.name.toLowerCase().includes(keyword.toLowerCase())
        );
      }
      
      setModules(filteredModules);
      setPagination(prev => ({
        ...prev,
        total: keyword ? filteredModules.length : response.total
      }));
    } catch (error) {
      showError(error, '获取模块列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, keyword, isRecycleBin, pagination.current, pagination.pageSize, frameworkId]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchModules();
    }
  }, [fetchModules]);

  // 处理名称搜索
  const handleSearch = (value: string) => {
    setKeyword(value);
    setKeywordValue(value);
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理搜索输入框值变化，但不触发搜索
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeywordValue(e.target.value);
  };

  // 新增模块
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setEditingModule(null);
    setIsModalVisible(true);
  };

  // 编辑模块
  const handleEdit = (module: ModuleResponse) => {
    setEditingModule(module);
    setIsModalVisible(true);
  };

  // 处理禁用模块（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个模块吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将模块禁用（设置active为0）
          await updateModule(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchModules();
        } catch (error: unknown) {
          showError(error, `${actionText}模块失败`);
        }
      },
    });
  };

  // 处理恢复模块
  const handleRestore = async (moduleId: number) => {
    try {
      await updateModule(moduleId, { active: 1 });
      showSuccess('恢复成功');
      fetchModules();
    } catch (error) {
      showError(error, '恢复模块失败');
    }
  };



  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setKeyword('');
    setKeywordValue('');

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 显式调用接口重新获取数据
    if (selectedTenantId) {
      await fetchModules();
    }
  };

  // 处理表格变化（只保留分页处理）
  const handleTableChange: TableProps<ModuleResponse>['onChange'] = (paginationConfig) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }
  };

  // 提交表单
  const handleSubmit = () => {
    setIsModalVisible(false);
    fetchModules();
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
        render: (description: string | null) => description || '-',
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        width: 100,
        render: (active: number) => {
          const status = statusMap[active] || { name: '未知', color: 'default' };
          return <Tag color={status.color}>{status.name}</Tag>;
        },
      },
    ];

    // 排序模式下的操作列
    if (isSortMode) {
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 80,
        render: (_: unknown, record: ModuleResponse) => (
          <Space size="middle">
            <Tooltip title="排序模式下不可编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                disabled={true}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: ModuleResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: ModuleResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: (
              <Link to="/system/framework">
                理论框架管理
              </Link>
            ),
          },
          {
            title: '模块管理',
          },
        ]}
      />

      <Card
        title={
          isSortMode 
            ? `${frameworkName} - 模块排序` 
            : (isRecycleBin ? `${frameworkName} - 模块回收站` : `${frameworkName} - 模块管理`)
        }
        extra={
          <Space>
            {/* 排序模式下只显示保存和取消按钮 */}
            {isSortMode ? (
              <>
                <Tooltip title="保存排序">
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSortSave}
                    loading={loading}
                    disabled={!modules.length}
                  />
                </Tooltip>
                <Tooltip title="取消排序">
                  <Button
                    type="default"
                    icon={<CloseOutlined />}
                    onClick={handleSortCancel}
                    disabled={loading}
                  />
                </Tooltip>
              </>
            ) : (
              <>
                <Search
                  placeholder="搜索模块名称"
                  allowClear
                  onSearch={handleSearch}
                  style={{ width: 160 }}
                  ref={searchKeywordInputRef}
                  value={keywordValue}
                  onChange={handleSearchInputChange}
                />
                {/* 添加模块按钮只在非回收站模式下显示 */}
                {!isRecycleBin && selectedTenantId && (
                  <Tooltip title="添加模块">
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAdd}
                    />
                  </Tooltip>
                )}
                <Tooltip title="重置刷新">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                  />
                </Tooltip>
                {/* 排序按钮 - 只在非回收站模式下显示 */}
                {!isRecycleBin && selectedTenantId && (
                  <Tooltip title="调整排序">
                    <Button
                      type="default"
                      icon={<DragOutlined />}
                      onClick={enterSortMode}
                    />
                  </Tooltip>
                )}
                {/* 回收站/返回按钮 */}
                {isRecycleBin ? (
                  <Tooltip title="返回">
                    <Button
                      type="primary"
                      icon={<RollbackOutlined />}
                      onClick={toggleRecycleBin}
                    />
                  </Tooltip>
                ) : (
                  <Tooltip title="回收站">
                    <Button
                      type="default"
                      icon={<DeleteFilled />}
                      onClick={toggleRecycleBin}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </Space>
        }
      >
        {isSortMode ? (
          <SortableModuleTable
            modules={modules}
            loading={loading}
            onModulesChange={handleModulesChange}
            onEdit={handleEdit}
          />
        ) : (
          <Table
            key={tableKey}
            columns={getColumns()}
            dataSource={modules}
            rowKey="id"
            loading={loading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            locale={{
              emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有模块' : '暂无模块数据') : '请先在顶部导航栏选择租户'
            }}
            onChange={handleTableChange}
            sortDirections={['ascend', 'descend', 'ascend']}
            showSorterTooltip={false}
          />
        )}
      </Card>

      {selectedTenantId && (
        <ModuleForm
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onSuccess={handleSubmit}
          module={editingModule}
          tenantId={selectedTenantId}
          frameworkId={frameworkId ? parseInt(frameworkId) : undefined}
        />
      )}
    </div>
  );
};

export default ModuleManagement; 