/* RichTextEditor 样式优化 */
.richTextEditorContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 确保包含富文本编辑器的Form.Item能够正确传递高度和宽度 */
:global(.rich-text-editor-form-wrapper .ant-form-item) {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  width: 100% !important;
}

:global(.rich-text-editor-form-wrapper .ant-form-item-row) {
  flex: 1 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

:global(.rich-text-editor-form-wrapper .ant-form-item-control) {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

:global(.rich-text-editor-form-wrapper .ant-form-item-control-input) {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

:global(.rich-text-editor-form-wrapper .ant-form-item-control-input-content) {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}


/* 强制 ReactQuill 使用 flex 布局 */
.richTextEditorContainer :global(.quill) {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

/* Quill 编辑器样式优化 */
.richTextEditorContainer :global(.ql-container) {
  flex: 1 !important;
  border: none !important;
  font-size: 14px;
  line-height: 1.6;
  display: flex !important;
  flex-direction: column !important;
}

.richTextEditorContainer :global(.ql-editor) {
  padding: 16px 20px;
  flex: 1 !important;
  line-height: 1.6;
  overflow-y: auto;
  min-height: 0;
}

.richTextEditorContainer :global(.ql-toolbar) {
  border: none !important;
  border-bottom: 1px solid #d9d9d9 !important;
  padding: 8px 12px;
  background-color: #fafafa;
}

/* 工具栏按钮样式 */
.richTextEditorContainer :global(.ql-toolbar .ql-formats) {
  margin-right: 12px;
}

.richTextEditorContainer :global(.ql-toolbar button) {
  margin-right: 4px;
  border-radius: 4px;
  padding: 4px 6px;
  transition: all 0.2s;
}

.richTextEditorContainer :global(.ql-toolbar button:hover) {
  background-color: #e6f7ff;
}

.richTextEditorContainer :global(.ql-toolbar button.ql-active) {
  background-color: #1890ff;
  color: white;
}

/* 下拉选择器样式 */
.richTextEditorContainer :global(.ql-toolbar .ql-picker) {
  color: #595959;
}

.richTextEditorContainer :global(.ql-toolbar .ql-picker-options) {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.richTextEditorContainer :global(.ql-toolbar .ql-picker-item:hover) {
  background-color: #f5f5f5;
}

/* 占位符样式 */
.richTextEditorContainer :global(.ql-editor.ql-blank::before) {
  color: #bfbfbf;
  font-style: normal;
  font-size: 14px;
}

/* 全屏模式样式 */
.fullscreenEditor {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1000 !important;
  background: white;
  width: 100vw !important;
  height: 100vh !important;
  min-height: 100vh !important;
}

.fullscreenEditor :global(.ql-editor) {
  padding: 24px 40px;
  width: 100%;
  margin: 0;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .richTextEditorContainer :global(.ql-toolbar) {
    padding: 6px 8px;
  }
  
  .richTextEditorContainer :global(.ql-editor) {
    padding: 12px 16px;
  }
  
  .fullscreenEditor :global(.ql-editor) {
    padding: 16px 20px;
    width: 100%;
    margin: 0;
  }
  
  .richTextEditorContainer :global(.ql-toolbar .ql-formats) {
    margin-right: 8px;
  }
}

/* 自定义滚动条 */
.richTextEditorContainer :global(.ql-editor) {
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
}

.richTextEditorContainer :global(.ql-editor::-webkit-scrollbar) {
  width: 6px;
}

.richTextEditorContainer :global(.ql-editor::-webkit-scrollbar-track) {
  background: transparent;
}

.richTextEditorContainer :global(.ql-editor::-webkit-scrollbar-thumb) {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.richTextEditorContainer :global(.ql-editor::-webkit-scrollbar-thumb:hover) {
  background-color: #bfbfbf;
}

/* 全高度 Tabs 样式 - 用于包含富文本编辑器的 Tabs 组件 */
.fullHeightTabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fullHeightTabs :global(.ant-tabs-content-holder) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fullHeightTabs :global(.ant-tabs-tabpane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fullHeightTabs :global(.ant-tabs-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}