import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Space, 
  Breadcrumb, 
  Spin, 
  Row,
  Col,
  Typography,
  theme,
  Tabs,
  message,
  Tooltip,
  Modal
} from 'antd';
import { 
  HomeOutlined, 
  RollbackOutlined, 
  EditOutlined,
  CheckOutlined,
  TagsOutlined,
  FileTextOutlined,
  CompassOutlined,
  CloseOutlined
} from '@ant-design/icons';
import QuestionContentTab, { type QuestionContentTabRef } from './QuestionContentTab';
import QuestionGuidesTab from './QuestionGuidesTab';
import QuestionTagsTab from './QuestionTagsTab';
import { useParams, useNavigate, Link, useBlocker } from 'react-router-dom';
import { 
  getQuestion, 
  updateQuestion,
  type QuestionResponse,
  getQuestionGuides,
  type QuestionGuideResponse
} from '../../../services/system/question';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import styles from '../../../components/RichTextEditor.module.css';

const { Title, Text } = Typography;

const QuestionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [question, setQuestion] = useState<QuestionResponse | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const { token } = theme.useToken();
  const questionContentTabRef = useRef<QuestionContentTabRef>(null);
  
  // 内联编辑状态
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [tempTitle, setTempTitle] = useState('');
  const [tempNotes, setTempNotes] = useState('');
  const [activeTab, setActiveTab] = useState('content');
  
  // 跟踪已访问的tab
  const [visitedTabs, setVisitedTabs] = useState<Set<string>>(new Set(['content']));
  
  // 确认对话框状态
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);
  
  // 问题指南
  const [guides, setGuides] = useState<QuestionGuideResponse[]>([]);
  
  // 标签状态（由QuestionTagsTab组件自己管理）
  const [selectedModuleIds, setSelectedModuleIds] = useState<number[]>([]);
  const [selectedSubjectIds, setSelectedSubjectIds] = useState<number[]>([]);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取问题详情
  const fetchQuestion = useCallback(async () => {
    if (!id) return;
    
    try {
      setInitialLoading(true);
      const response = await getQuestion(parseInt(id));
      setQuestion(response);
      form.setFieldsValue({
        title: response.title,
        bgtext: response.bgtext,
        notes: response.notes,
      });
      setTempTitle(response.title);
      setTempNotes(response.notes || '');
    } catch (error) {
      showError(error, '获取问题详情失败');
      navigate('/system/question');
    } finally {
      setInitialLoading(false);
    }
  }, [id, form, navigate]);

  // 保存标题
  const handleSaveTitle = async () => {
    if (!question || !tempTitle.trim()) return;
    
    try {
      await updateQuestion(question.id, { title: tempTitle.trim() });
      setQuestion({ ...question, title: tempTitle.trim() });
      setEditingTitle(false);
      message.success('标题更新成功');
    } catch (error) {
      showError(error, '更新标题失败');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!question) return;
    
    try {
      await updateQuestion(question.id, { notes: tempNotes });
      setQuestion({ ...question, notes: tempNotes });
      setEditingNotes(false);
      message.success('备注更新成功');
    } catch (error) {
      showError(error, '更新备注失败');
    }
  };

  // 开始编辑标题
  const startEditTitle = () => {
    setTempTitle(question?.title || '');
    setEditingTitle(true);
  };

  // 开始编辑备注
  const startEditNotes = () => {
    setTempNotes(question?.notes || '');
    setEditingNotes(true);
  };

  // 取消编辑
  const cancelEditTitle = () => {
    setTempTitle(question?.title || '');
    setEditingTitle(false);
  };

  const cancelEditNotes = () => {
    setTempNotes(question?.notes || '');
    setEditingNotes(false);
  };

  // 获取问题指南
  const fetchGuides = useCallback(async () => {
    if (!selectedTenantId || !id) return;
    
    try {
      const response = await getQuestionGuides({
        tenant_id: selectedTenantId,
        question_id: parseInt(id),
        sort_by: 'priority',
        sort_order: 'asc'
      });
      setGuides(response.items);
    } catch (error) {
      showError(error, '获取问题指南失败');
    }
  }, [selectedTenantId, id]);

  // 处理题干内容保存成功
  const handleBgtextSaveSuccess = useCallback((updatedBgtext: string) => {
    if (question) {
      setQuestion({ ...question, bgtext: updatedBgtext });
    }
  }, [question]);



  // 检查是否有未保存的更改
  const checkUnsavedChanges = () => {
    return questionContentTabRef.current?.hasUnsavedChanges() || false;
  };

  // 显示确认对话框
  const showConfirmModal = (action: () => void) => {
    if (checkUnsavedChanges()) {
      setPendingAction(() => action);
      setShowUnsavedModal(true);
    } else {
      action();
    }
  };

  // 处理保存并继续
  const handleSaveAndContinue = async () => {
    try {
      await questionContentTabRef.current?.saveContent();
      setShowUnsavedModal(false);
      if (pendingAction) {
        pendingAction();
        setPendingAction(null);
      }
    } catch {
      // 保存失败，不执行待定操作
    }
  };

  // 处理不保存直接继续
  const handleDiscardAndContinue = () => {
    // 恢复到初始内容
    questionContentTabRef.current?.resetToInitial();
    setShowUnsavedModal(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setShowUnsavedModal(false);
    setPendingAction(null);
  };

  // 使用路由阻塞器
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      currentLocation.pathname !== nextLocation.pathname && checkUnsavedChanges()
  );

  useEffect(() => {
    fetchQuestion();
  }, [fetchQuestion]);

  // 当租户切换时，重置已访问的tab
  useEffect(() => {
    if (selectedTenantId) {
      setVisitedTabs(new Set(['content']));
    }
  }, [selectedTenantId]);

  // 监听页面卸载事件
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (checkUnsavedChanges()) {
        e.preventDefault();
        e.returnValue = '您有未保存的内容，确定要离开吗？';
        return '您有未保存的内容，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // 处理路由阻塞
  useEffect(() => {
    if (blocker.state === 'blocked') {
      showConfirmModal(() => {
        blocker.proceed();
      });
    }
  }, [blocker]);

  // 处理tab切换，只在第一次访问时加载数据
  const handleTabChange = async (tabKey: string) => {
    const performTabChange = async () => {
      setActiveTab(tabKey);
      
      if (!visitedTabs.has(tabKey) && selectedTenantId) {
        setVisitedTabs(prev => new Set(prev).add(tabKey));
        
        switch (tabKey) {
          case 'guides':
            await fetchGuides();
            break;
          case 'tags':
            // 标签tab的数据由子组件自己管理，无需在这里请求
            break;
        }
      }
    };

    if (activeTab === 'content') {
      // 从内容tab切换出去时检查未保存更改
      showConfirmModal(performTabChange);
    } else {
      performTabChange();
    }
  };



  const handleBack = () => {
    showConfirmModal(() => {
      navigate('/system/question');
    });
  };

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!question) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Text type="secondary">问题不存在</Text>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 8 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: (
              <Link to="/system/question">
                问题库
              </Link>
            ),
          },
          {
            title: '问题详情',
          },
        ]}
      />

      <div style={{ backgroundColor: token.colorBgContainer, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 头部区域 */}
        <div style={{ 
          backgroundColor: '#fff', 
          padding: '16px 0px', 
          borderBottom: `1px solid ${token.colorBorderSecondary}`,
          marginBottom: '12px',
          minHeight: '120px'
        }}>
          {/* 问题标题和操作区 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
                        <div style={{ flex: 1, minWidth: '300px' }}>
              {/* 问题标题（可编辑） */}
              <div style={{ 
                display: 'flex', 
                alignItems: 'flex-end', 
                marginBottom: '12px',
                minHeight: '40px'
              }}>
                {editingTitle ? (
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    flex: 1,
                    height: '40px'
                  }}>
                    <Input
                      value={tempTitle}
                      onChange={(e) => setTempTitle(e.target.value)}
                      style={{ fontSize: '24px', fontWeight: 600, marginRight: '8px' }}
                      size="large"
                      onPressEnter={handleSaveTitle}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditTitle();
                        }
                      }}
                      autoFocus
                    />
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<CheckOutlined />}
                        onClick={handleSaveTitle}
                      />
                      <Button
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={cancelEditTitle}
                      />
                    </Space>
                  </div>
                ) : (
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'flex-end', 
                    flex: 1,
                    height: '40px'
                  }}>
                    <Title 
                      level={2} 
                      style={{ 
                        margin: 0, 
                        marginRight: '8px',
                        cursor: 'pointer',
                        lineHeight: '40px'
                      }}
                      onClick={startEditTitle}
                    >
                      {question?.title || '加载中...'}
                    </Title>
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      size="small"
                      onClick={startEditTitle}
                      style={{ color: token.colorTextSecondary, marginBottom: '4px' }}
                    />
                  </div>
                )}
              </div>
              
              {/* 备注（可编辑） */}
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                minHeight: '32px'
              }}>
                <Text style={{ fontSize: '14px', marginRight: '8px', lineHeight: '32px' }}>
                  备注：
                </Text>
                {editingNotes ? (
                  <div style={{ 
                    flex: 1, 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '8px',
                    height: '32px'
                  }}>
                    <Input
                      value={tempNotes}
                      onChange={(e) => setTempNotes(e.target.value)}
                      placeholder="添加备注..."
                      style={{ flex: 1 }}
                      autoFocus
                      onPressEnter={handleSaveNotes}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditNotes();
                        }
                      }}
                    />
                    <Button
                      type="primary"
                      size="small"
                      icon={<CheckOutlined />}
                      onClick={handleSaveNotes}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditNotes}
                    />
                  </div>
                ) : (
                  <Text 
                    type="secondary" 
                    style={{ 
                      fontSize: '14px', 
                      cursor: 'pointer',
                      flex: 1,
                      lineHeight: '32px'
                    }}
                    onClick={startEditNotes}
                  >
                    {question?.notes || '点击添加备注...'}
                  </Text>
                )}
              </div>
            </div>
            
            <Space size="middle">
              <Tooltip title="返回列表">
                <Button
                  icon={<RollbackOutlined />}
                  onClick={handleBack}
                  size="middle"
                  type="primary"
                />
              </Tooltip>
            </Space>
          </div>
        </div>

        {/* 主体内容区域 */}
        <div style={{ padding: '0 8px', flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Row gutter={[24, 24]} style={{ flex: 1, display: 'flex' }}>
            {/* 主要内容 */}
            <Col xs={24} style={{ display: 'flex', flexDirection: 'column' }}>
              {/* 标签页 */}
              <div style={{ 
                flex: 1,
                display: 'flex', 
                flexDirection: 'column' 
              }}>
                <Tabs
                  activeKey={activeTab}
                  onChange={handleTabChange}
                  destroyOnHidden={true}
                  style={{ 
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                  className={styles.fullHeightTabs}
                  items={[
                  {
                    key: 'content',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <FileTextOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        题干内容
                      </span>
                    ),
                                          children: <QuestionContentTab 
                        ref={questionContentTabRef}
                        form={form} 
                        questionId={question?.id} 
                        initialBgtext={question?.bgtext || ''}
                        onSaveSuccess={handleBgtextSaveSuccess}
                      />,
                  },
                  {
                    key: 'guides',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <CompassOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        答题指南
                      </span>
                    ),
                    children: (
                      <QuestionGuidesTab
                        guides={guides}
                        tenantId={selectedTenantId}
                        questionId={id}
                        onRefresh={fetchGuides}
                        onGuidesChange={setGuides}
                      />
                    ),
                  },
                  {
                    key: 'tags',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <TagsOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        标签
                      </span>
                    ),
                    children: (
                      <QuestionTagsTab
                        tenantId={selectedTenantId}
                        questionId={id}
                        selectedModuleIds={selectedModuleIds}
                        selectedSubjectIds={selectedSubjectIds}
                        setSelectedModuleIds={setSelectedModuleIds}
                        setSelectedSubjectIds={setSelectedSubjectIds}
                      />
                    ),
                  },
                ]}
              />
              </div>
            </Col>
          </Row>
                </div>
      </div>

      {/* 未保存更改确认对话框 */}
      <Modal
        title="未保存的更改"
        open={showUnsavedModal}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="discard" onClick={handleDiscardAndContinue}>
            不保存
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveAndContinue}>
            保存并继续
          </Button>,
        ]}
        closable={false}
        maskClosable={false}
      >
        <p>您有未保存的题干内容更改，是否要保存？</p>
      </Modal>
    </div>
  );
};

export default QuestionDetail; 