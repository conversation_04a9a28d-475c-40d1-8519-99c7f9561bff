import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore, UserType } from '../../../utils/authStore';
import SystemLoginForm from './components/SystemLoginForm';
import '../../../styles/login.css';
import '../../../styles/particles.css';
import '../../../styles/tech-pattern.css';

const SystemLogin: React.FC = () => {
  const { isAuthenticated, userType, isLoading } = useAuthStore();
  const navigate = useNavigate();

  // 如果已经登录且是系统管理员，重定向到系统管理页面
  useEffect(() => {
    if (!isLoading && isAuthenticated && userType === UserType.SYSTEM) {
      navigate('/system', { replace: true });
    }
  }, [isAuthenticated, userType, isLoading, navigate]);

  return (
    <div className="login-container">
      {/* 添加科技感背景图案 */}
      <div className="tech-pattern"></div>

      {/* 添加粒子效果 */}
      <div className="particles">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>
      <div className="login-content">
        <div className="login-top">
          <div className="login-header">
            <span className="login-title">TMAI系统管理后台</span>
          </div>
          <div className="login-desc">专业的TMAI系统管理平台</div>
        </div>

        <div className="login-main">
          <div className="login-card">
            <SystemLoginForm />
          </div>
        </div>

        <div className="login-footer">
          <div className="login-footer-copyright">
            Copyright © {new Date().getFullYear()} TMAI系统管理后台
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemLogin; 