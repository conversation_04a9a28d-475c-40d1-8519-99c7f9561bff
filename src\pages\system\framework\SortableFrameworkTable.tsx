import React, { useState, useMemo } from 'react';
import { Table, Button, Space, Tooltip, Tag, Avatar } from 'antd';
import { EditOutlined, HolderOutlined, FileImageOutlined } from '@ant-design/icons';
import '../../../styles/sortable-table.css';
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  restrictToVerticalAxis,
} from '@dnd-kit/modifiers';
import { type FrameworkResponse } from '../../../services/system/framework';

// 状态映射常量
const statusMap: { [key: number]: { name: string; color: string } } = {
  0: { name: '禁用', color: 'red' },
  1: { name: '启用', color: 'green' },
};

// 可拖拽的表格行组件
interface DraggableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
  children: React.ReactNode;
}

const DraggableRow: React.FC<DraggableRowProps> = React.memo(({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transition: isDragging ? 'none' : (transition || 'transform 200ms cubic-bezier(0.645, 0.045, 0.355, 1)'),
    cursor: isDragging ? 'grabbing' : 'default',
    ...(isDragging ? { 
      opacity: 0.5, // 拖拽时让原始行变半透明
      transform: CSS.Transform.toString(transform),
    } : {
      transform: CSS.Transform.toString(transform),
      transition: 'all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)',
    }),
  };

  // 将子元素转换为数组以便插入拖拽手柄
  const childrenArray = React.Children.toArray(children);
  
  // 在第一列后插入拖拽手柄列
  if (Array.isArray(childrenArray) && childrenArray.length > 0) {
    // 为第一个 td 添加拖拽手柄
    const firstCell = childrenArray[0] as React.ReactElement;
    const modifiedFirstCell = React.cloneElement(firstCell, {
      children: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span
            {...attributes}
            {...listeners}
            style={{
              cursor: 'grab',
              color: '#8c8c8c',
              fontSize: '14px',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '4px',
              transition: 'all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)',
              backgroundColor: 'transparent',
              width: '20px',
              height: '20px',
              border: '1px solid transparent',
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
            onMouseUp={() => {
              // 如果不在拖拽状态，恢复默认鼠标样式
              if (!document.body.classList.contains('dragging')) {
                document.body.style.cursor = 'default';
              }
            }}
            className="drag-handle"
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.classList.add('hover');
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.classList.remove('hover');
            }}
          >
            <HolderOutlined />
          </span>
          {firstCell.props.children}
        </div>
      )
    });
    
    childrenArray[0] = modifiedFirstCell;
  }

  return (
    <tr 
      {...props} 
      ref={setNodeRef} 
      style={style}
      className={isDragging ? 'dragging-row' : ''}
    >
      {childrenArray}
    </tr>
  );
});

// 排序表格组件的Props接口
interface SortableFrameworkTableProps {
  frameworks: FrameworkResponse[];
  loading: boolean;
  onFrameworksChange: (frameworks: FrameworkResponse[]) => void;
  onEdit: (framework: FrameworkResponse) => void;
  onUploadLogo: (framework: FrameworkResponse) => void;
}

const SortableFrameworkTable: React.FC<SortableFrameworkTableProps> = ({
  frameworks,
  loading,
  onFrameworksChange,
  onEdit,
  onUploadLogo,
}) => {
  // 拖拽覆盖层状态
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<FrameworkResponse | null>(null);
  const [overId, setOverId] = useState<string | null>(null);

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽 8px 才激活
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: (event) => {
        // 键盘导航的坐标获取器
        if (event.code === 'ArrowDown') {
          return { x: 0, y: 1 };
        }
        if (event.code === 'ArrowUp') {
          return { x: 0, y: -1 };
        }
        return undefined;
      },
    })
  );



  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    
    const draggedFramework = frameworks.find(item => item.id.toString() === active.id);
    setDraggedItem(draggedFramework || null);
    
    // 添加全局拖拽状态，让整个页面都显示十字形鼠标
    document.body.classList.add('dragging');
    document.body.style.cursor = 'grabbing';
  };

  // 处理拖拽悬停
  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    setOverId(over ? over.id as string : null);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setDraggedItem(null);
    setOverId(null);
    
    // 移除全局拖拽状态，恢复默认鼠标样式
    document.body.classList.remove('dragging');
    document.body.style.cursor = 'default';

    if (over && active.id !== over.id) {
      const oldIndex = frameworks.findIndex(item => item.id.toString() === active.id.toString());
      const newIndex = frameworks.findIndex(item => item.id.toString() === over.id.toString());
      
      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
        const newFrameworks = arrayMove(frameworks, oldIndex, newIndex);
        onFrameworksChange(newFrameworks);
      }
    }
  };



  // 表格列定义
  const columns = useMemo(() => [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: 'LOGO',
      dataIndex: 'logo',
      key: 'logo',
      width: 80,
      render: (logo: string | null, record: FrameworkResponse) => (
        <div
          style={{ cursor: 'pointer' }}
          onClick={() => onUploadLogo(record)}
        >
          <Avatar
            size={40}
            src={logo}
            icon={<FileImageOutlined />}
            shape="square"
            style={{ backgroundColor: '#f0f0f0' }}
          />
        </div>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string | null) => description || '-',
    },
    {
      title: '状态',
      dataIndex: 'active',
      key: 'active',
      width: 100,
      render: (active: number) => {
        const status = statusMap[active] || { name: '未知', color: 'default' };
        return <Tag color={status.color}>{status.name}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: unknown, record: FrameworkResponse) => (
        <Space size="middle">
          <Tooltip title="排序模式下不可编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              disabled={true}
              onClick={() => onEdit(record)}
            />
          </Tooltip>
        </Space>
      ),
    } as never,
  ], [statusMap, onEdit, onUploadLogo]);

  return (
    <>
      {/* 可拖拽的表格 */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext
          items={frameworks.map(item => item.id.toString())}
          strategy={verticalListSortingStrategy}
        >
          <Table
            columns={columns}
            dataSource={frameworks}
            rowKey={(record) => record.id.toString()}
            loading={loading}
            pagination={false}
            virtual={frameworks.length > 100} // 大数据集时启用虚拟滚动
            scroll={frameworks.length > 100 ? { y: 400 } : undefined}
            locale={{
              emptyText: '暂无理论框架数据'
            }}
            components={{
              body: {
                row: DraggableRow,
              },
            }}
            showSorterTooltip={false}
            size="middle" // 使用紧凑尺寸提高性能
            rowClassName={(record) => {
              // 添加插入指示器类名 - 确保类型一致性
              const recordIdStr = record.id.toString();
              const overIdStr = overId ? overId.toString() : null;
              const activeIdStr = activeId ? activeId.toString() : null;
              
              const isDropTarget = overIdStr && recordIdStr === overIdStr && activeIdStr && activeIdStr !== overIdStr;
              
              if (isDropTarget) {
                return 'drop-indicator';
              }
              return '';
            }}
          />
        </SortableContext>
        
        <DragOverlay>
          {activeId && draggedItem ? (
            <table style={{ 
              backgroundColor: '#ffffff',
              boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)',
              borderRadius: '6px',
              border: '1px solid #d9d9d9',
              minWidth: '600px',
            }}>
              <tbody>
                <tr style={{ backgroundColor: '#ffffff' }}>
                  <td style={{ padding: '12px 16px', border: 'none', display: 'flex', alignItems: 'center', gap: 8 }}>
                    <HolderOutlined style={{ color: '#8c8c8c' }} />
                    {draggedItem.id}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none', width: '80px' }}>
                    <Avatar
                      size={40}
                      src={draggedItem.logo}
                      icon={<FileImageOutlined />}
                      shape="square"
                      style={{ backgroundColor: '#f0f0f0' }}
                    />
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none', fontWeight: 500 }}>
                    {draggedItem.name}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none', color: '#595959' }}>
                    {draggedItem.description && draggedItem.description.length > 30 
                      ? `${draggedItem.description.slice(0, 30)}...` 
                      : draggedItem.description || '-'}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none' }}>
                    <Tag color={statusMap[draggedItem.active]?.color || 'default'}>
                      {statusMap[draggedItem.active]?.name || '未知'}
                    </Tag>
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none' }}>
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      size="small"
                      disabled={true}
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  );
};

export default SortableFrameworkTable; 