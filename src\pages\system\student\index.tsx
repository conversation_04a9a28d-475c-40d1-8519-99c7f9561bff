import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, DatePicker, Tag, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  getStudents, 
  updateStudent,
  type StudentResponse
} from '../../../services/system/student';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import StudentForm from './StudentForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

// 性别映射
const genderMap: Record<number, { name: string; color: string }> = {
  0: { name: '未知', color: 'default' },
  1: { name: '男', color: 'blue' },
  2: { name: '女', color: 'pink' },
};

const StudentManagement: React.FC = () => {
  const [students, setStudents] = useState<StudentResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentStudent, setCurrentStudent] = useState<StudentResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储姓名输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  // 创建对搜索框和日期选择器的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const datePickerRef = useRef<any>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取学员列表
  const fetchStudents = useCallback(async () => {
    if (!selectedTenantId) {
      setStudents([]);
      return;
    }

    try {
      setLoading(true);
      
      let sortBy: 'id' | 'ctime' | undefined;
      let sortOrder: 'asc' | 'desc' | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getStudents(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
      });
      
      // 手动过滤数据（因为接口不支持name搜索）
      let filteredStudents = [...response.items];
      
      // 手动实现姓名搜索过滤
      if (searchName) {
        filteredStudents = filteredStudents.filter(student => 
          student.name.toLowerCase().includes(searchName.toLowerCase())
        );
      }
      
      setStudents(filteredStudents);
      setPagination(prev => ({
        ...prev,
        total: filteredStudents.length
      }));
    } catch (error) {
      showError(error, '获取学员列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, dateRange, sortOrders, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchStudents();
    }
  }, [fetchStudents]);

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理日期范围变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      // 转换为ISO 8601格式，保持本地时区
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加学员
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentStudent(null);
    setModalVisible(true);
  };

  // 处理编辑学员
  const handleEdit = (record: StudentResponse) => {
    setCurrentStudent(record);
    setModalVisible(true);
  };

  // 处理禁用学员（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个学员吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将学员禁用（设置active为0）
          await updateStudent(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchStudents();
        } catch (error: unknown) {
          showError(error, `${actionText}学员失败`);
        }
      },
    });
  };

  // 处理恢复学员
  const handleRestore = async (studentId: number) => {
    try {
      await updateStudent(studentId, { active: 1 });
      showSuccess('恢复成功');
      fetchStudents();
    } catch (error) {
      showError(error, '恢复学员失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchStudents();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');

    // 清空时间区间控件
    setDateRange(null);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 数据会通过useEffect自动重新加载
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<StudentResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username',
        render: (username: string) => username || '-',
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '性别',
        dataIndex: 'gender',
        key: 'gender',
        render: (gender: number) => {
          const genderInfo = genderMap[gender] || { name: '未知', color: 'default' };
          return <Tag color={genderInfo.color}>{genderInfo.name}</Tag>;
        },
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        render: (notes: string) => notes || '-',
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        render: (text: string) => text ? new Date(text).toLocaleString() : '-',
        sorter: true,
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : undefined,
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        render: (active: number | undefined) => {
          if (active === undefined) {
            return <span style={{ color: 'green' }}>启用</span>; // 默认为启用状态
          }
          return (
            <span style={{ color: active === 1 ? 'green' : 'red' }}>
              {active === 1 ? '启用' : '禁用'}
            </span>
          );
        },
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: StudentResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: StudentResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '学员管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "学员回收站" : "学员管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索姓名"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              ref={datePickerRef}
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {/* 添加学员按钮只在非回收站模式下显示 */}
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加学员">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={students}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有学员' : '暂无学员数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <StudentForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          student={currentStudent}
          tenantId={selectedTenantId}
        />
      )}
    </div>
  );
};

export default StudentManagement; 