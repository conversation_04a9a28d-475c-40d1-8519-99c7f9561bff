import { get, post, put, del } from '../api';

// 机器人数据类型（根据openapi更新）
export interface Bot {
  id: number;
  name: string;
  api_endpoint?: string;
  api_key?: string;
  notes?: string;
  sys_prompt?: string;
  active: number;
}

// 机器人创建请求（根据openapi更新）
export interface BotCreate {
  name: string;
  api_endpoint?: string;
  api_key?: string;
  notes?: string;
  sys_prompt?: string;
}

// 机器人更新请求（根据openapi更新）
export interface BotUpdate {
  name?: string;
  api_endpoint?: string;
  api_key?: string;
  notes?: string;
  sys_prompt?: string;
  active?: number;
}

// 机器人列表响应
export interface BotListResponse {
  total: number;
  items: Bot[];
}

// 获取机器人列表（支持更多参数）
export const getBots = async (params: {
  skip?: number;
  limit?: number;
  keyword?: string;
  active?: number;
  sort_by?: string;
  sort_order?: string;
}): Promise<BotListResponse> => {
  return get('/sys/bot', params);
};

// 创建机器人
export const createBot = async (data: BotCreate): Promise<Bot> => {
  return post('/sys/bot', data);
};

// 获取机器人详情
export const getBot = async (botId: number): Promise<Bot> => {
  return get(`/sys/bot/${botId}`);
};

// 更新机器人
export const updateBot = async (botId: number, data: BotUpdate): Promise<Bot> => {
  return put(`/sys/bot/${botId}`, data);
};

// 删除机器人
export const deleteBot = async (botId: number): Promise<void> => {
  return del(`/sys/bot/${botId}`);
}; 