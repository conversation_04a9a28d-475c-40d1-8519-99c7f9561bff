import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 用户类型
export interface User {
  token: string;
  uid: number;
  username: string;
  name: string;
  role: number;
  tenant_id?: number;
}

// 用户类型枚举
export enum UserType {
  SYSTEM = 'system',
  TENANT = 'tenant'
}

// 认证状态
interface AuthState {
  user: User | null;
  userType: UserType | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: User, type: UserType) => void;
  logout: () => void;
  updateProfile: (profileData: Omit<User, 'token'>) => void;
}

// 创建认证状态存储
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      userType: null,
      isAuthenticated: false,
      isLoading: true,
      login: (user: User, type: UserType) => set({
        user,
        userType: type,
        isAuthenticated: true,
        isLoading: false
      }),
      logout: () => set({
        user: null,
        userType: null,
        isAuthenticated: false,
        isLoading: false
      }),
      updateProfile: (profileData: Omit<User, 'token'>) => {
        const currentState = get();
        if (currentState.user) {
          set({
            user: {
              ...currentState.user,
              ...profileData,
              // 保持原有的token不变
              token: currentState.user.token
            }
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      onRehydrateStorage: () => (state) => {
        // 确保在重新加载后总是设置 isLoading 为 false
        if (state) {
          // 设置认证状态
          const hasValidUser = !!(state.user && state.user.token && state.userType === UserType.SYSTEM);
          state.isAuthenticated = hasValidUser;
          // 无论如何都要设置 isLoading 为 false
          state.isLoading = false;
        }
      },
      partialize: (state) => ({
        user: state.user,
        userType: state.userType,
        isAuthenticated: state.isAuthenticated,
        // 不持久化 isLoading，每次启动都是 true
      }),
    }
  )
); 