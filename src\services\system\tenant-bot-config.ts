import { get, post, put, del } from '../api';

// 租户机器人配置数据类型
export interface TenantBotConfig {
  tenant_id: number;
  key: string;
  bid: number;
  bot_name: string; // 必需字段：机器人名称
  bot_active: number; // 必需字段：机器人是否有效（0：失效；1：有效）
  notes?: string; // 可选字段：备注
  bot_api_endpoint?: string; // 可选字段：机器人API路径
  bot_api_key?: string; // 可选字段：机器人API密钥
  bot_sys_prompt?: string; // 可选字段：机器人系统提示
  bot_notes?: string; // 可选字段：机器人备注
}

// 租户机器人配置创建请求
export interface TenantBotConfigCreate {
  tenant_id: number;
  key: string;
  bid: number;
  notes?: string;
}

// 租户机器人配置更新请求
export interface TenantBotConfigUpdate {
  tenant_id?: number;
  key?: string; // 可选：配置键名
  bid?: number; // 可选：机器人ID
  notes?: string; // 可选：备注
}

// 租户机器人配置列表响应
export interface TenantBotConfigListResponse {
  total: number;
  items: TenantBotConfig[];
}

// 获取租户机器人配置列表
export const getTenantBotConfigs = async (params: {
  skip?: number;
  limit?: number;
  keyword?: string;
}): Promise<TenantBotConfigListResponse> => {
  return get('/sys/tnt-bconf', params);
};

// 创建租户机器人配置
export const createTenantBotConfig = async (data: TenantBotConfigCreate): Promise<TenantBotConfig> => {
  return post('/sys/tnt-bconf', data);
};

// 获取租户机器人配置详情
export const getTenantBotConfig = async (tenantId: number, key: string): Promise<TenantBotConfig> => {
  return get(`/sys/tnt-bconf/${tenantId}/${key}`);
};

// 更新租户机器人配置
export const updateTenantBotConfig = async (tenantId: number, key: string, data: TenantBotConfigUpdate): Promise<TenantBotConfig> => {
  return put(`/sys/tnt-bconf/${tenantId}/${key}`, data);
};

// 删除租户机器人配置
export const deleteTenantBotConfig = async (tenantId: number, key: string): Promise<void> => {
  return del(`/sys/tnt-bconf/${tenantId}/${key}`);
}; 