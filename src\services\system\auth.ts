import { post, get, put } from '../api';
import { User, useAuthStore } from '../../utils/authStore';
import { encryptPassword } from '../../utils/crypto';

// 系统管理员登录请求接口
interface LoginRequest {
  username: string;
  password: string;
}

// 系统管理员登录响应接口
interface LoginResponse {
  token: string;
  token_type: string;
  uid: number;
  username: string;
  name: string;
  role: number;
}

// 系统管理员个人信息接口
interface SysAdminProfile {
  uid: number;
  username: string;
  name: string;
  role: number;
}

// 密码更新接口
interface SysAdminPasswordUpdate {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

// 登出响应接口
interface LogoutResponse {
  message: string;
}

// 密码更新请求类型
export interface PasswordUpdateRequest {
  old_password: string;
  new_password: string;
}

// 系统管理员登录
export const login = async (username: string, password: string): Promise<User> => {
  // 使用SHA256加密密码
  const encryptedPassword = encryptPassword(password);

  const data: LoginRequest = {
    username,
    password: encryptedPassword,
  };

  const response = await post<LoginResponse>('/sys/auth/login', data);

  // 直接从登录响应构造用户数据，不再调用profile接口
  return {
    token: response.token,
    uid: response.uid,
    username: response.username,
    name: response.name,
    role: response.role,  
  };
};

// 获取当前管理员个人信息
export const getProfile = async (token?: string): Promise<SysAdminProfile> => {
  const config = token ? { 
    headers: { Authorization: `Bearer ${token}` } 
  } : undefined;
  
  return await get<SysAdminProfile>('/sys/auth/profile', undefined, config);
};

// 获取profile信息并更新本地存储（保持token不变）
export const updateProfileInStorage = async (): Promise<void> => {
  const profile = await getProfile();
  const { updateProfile } = useAuthStore.getState();
  
  // 更新本地存储中除token之外的数据
  updateProfile({
    uid: profile.uid,
    username: profile.username,
    name: profile.name,
    role: profile.role,
  });
};

// 系统管理员登出
export const logout = async (): Promise<void> => {
  await post<LogoutResponse>('/sys/auth/logout');
};

// 修改密码
export const changePassword = async (
  oldPassword: string, 
  newPassword: string, 
  confirmPassword: string
): Promise<void> => {
  // 使用SHA256加密密码
  const encryptedOldPassword = encryptPassword(oldPassword);
  const encryptedNewPassword = encryptPassword(newPassword);
  const encryptedConfirmPassword = encryptPassword(confirmPassword);

  const data: SysAdminPasswordUpdate = {
    old_password: encryptedOldPassword,
    new_password: encryptedNewPassword,
    confirm_password: encryptedConfirmPassword,
  };

  await put('/sys/auth/password', data);
};

// 更新密码
export const updatePassword = async (data: PasswordUpdateRequest): Promise<void> => {
  return put('/sys/auth/password', data);
}; 