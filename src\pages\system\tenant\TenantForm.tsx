import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createTenant, 
  updateTenant,
  type Tenant,
  type TenantCreate,
  type TenantUpdate 
} from '../../../services/system/tenant';
import { showError, showSuccess } from '../../../utils/errorHandler';

interface TenantFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  tenant: Tenant | null;
}

const TenantForm: React.FC<TenantFormProps> = ({ visible, onCancel, onSuccess, tenant }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (tenant) {
        form.setFieldsValue({
          code: tenant.code,
          name: tenant.name,
          notes: tenant.notes,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, tenant, form]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (tenant) {
        const updateData: TenantUpdate = {
          code: values.code,
          name: values.name,
          notes: values.notes,
        };
        
        await updateTenant(tenant.id, updateData);
        showSuccess('更新租户成功');
      } else {
        const createData: TenantCreate = {
          code: values.code,
          name: values.name,
          notes: values.notes,
        };
        await createTenant(createData);
        showSuccess('创建租户成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={tenant ? '编辑租户' : '添加租户'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="code"
          label="租户代号"
          rules={[{ required: true, message: '请输入租户代号' }]}
        >
          <Input placeholder="请输入租户代号" />
        </Form.Item>

        <Form.Item
          name="name"
          label="租户名称"
          rules={[{ required: true, message: '请输入租户名称' }]}
        >
          <Input placeholder="请输入租户名称" />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <Input.TextArea 
            placeholder="请输入备注" 
            rows={3}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TenantForm; 