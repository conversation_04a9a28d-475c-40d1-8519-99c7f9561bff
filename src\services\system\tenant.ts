import { get, post, put, del } from '../api';

// 租户数据类型
export interface Tenant {
  id: number;
  code: string;
  name: string;
  notes?: string;
  active: number;
  ctime: string;
}

// 租户创建请求
export interface TenantCreate {
  code: string;
  name: string;
  notes?: string;
}

// 租户更新请求
export interface TenantUpdate {
  code?: string;
  name?: string;
  notes?: string;
  active?: number;
}

// 租户列表响应
export interface TenantListResponse {
  total: number;
  items: Tenant[];
}

// 获取租户列表
export const getTenants = async (params: {
  skip?: number;
  limit?: number;
  keyword?: string;
  active?: number;
  sort_by?: string;
  sort_order?: string;
  start_time?: string;
  end_time?: string;
}): Promise<TenantListResponse> => {
  return get('/sys/tenant', params);
};

// 创建租户
export const createTenant = async (data: TenantCreate): Promise<Tenant> => {
  return post('/sys/tenant', data);
};

// 获取租户详情
export const getTenant = async (tenantId: number): Promise<Tenant> => {
  return get(`/sys/tenant/${tenantId}`);
};

// 更新租户
export const updateTenant = async (tenantId: number, data: TenantUpdate): Promise<Tenant> => {
  return put(`/sys/tenant/${tenantId}`, data);
};

// 删除租户
export const deleteTenant = async (tenantId: number): Promise<void> => {
  return del(`/sys/tenant/${tenantId}`);
}; 