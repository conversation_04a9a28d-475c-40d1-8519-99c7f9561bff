/* 详情页面通用样式 */

/* 页面布局 */
.detail-page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-page-header {
  background-color: #fff;
  padding: 8px 0px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0px;
  min-height: 180px;
}

.detail-page-content {
  padding: 0 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 标题行 */
.detail-title-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 16px;
  min-height: 36px;
}

.detail-title-left {
  display: flex;
  align-items: baseline;
  width: 75%;
  height: 36px;
}

.detail-title-right {
  display: flex;
  align-items: baseline;
  gap: 12px;
  height: 36px;
  margin-left: auto;
}

.detail-title {
  margin: 0;
  margin-right: 4px;
  cursor: pointer;
  line-height: 36px;
  font-size: 20px;
}

.detail-title-input {
  font-size: 20px;
  font-weight: 600;
  margin-right: 8px;
  flex: 1;
}

.detail-create-time {
  font-size: 13px;
  line-height: 36px;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.45);
}

/* 字段样式 */
.detail-field-row {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.detail-field-label {
  font-size: 14px;
  min-width: 35px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.45);
}

.detail-field-content {
  font-size: 14px;
  cursor: pointer;
  flex: 1;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  transition: background-color 0.2s;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.detail-field-content:hover {
  background-color: #f5f5f5;
}

.detail-field-content.empty {
  color: rgba(0, 0, 0, 0.25);
  font-style: italic;
}

.detail-field-content.has-value {
  color: rgba(0, 0, 0, 0.88);
  font-style: normal;
}

/* 编辑状态样式 */
.detail-edit-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-edit-input {
  flex: 1;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
}

.detail-edit-input:focus {
  background-color: #fff;
}

/* 按钮样式 */
.detail-edit-btn {
  border: none;
  box-shadow: none;
}

.detail-edit-btn.primary {
  background-color: #1677ff;
  color: #fff;
}

.detail-edit-btn.default {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.88);
  border: 1px solid #d9d9d9;
}

/* 发布状态按钮 */
.detail-publish-btn.published {
  background-color: #1677ff;
  border-color: #1677ff;
  color: #fff;
}

.detail-publish-btn.draft {
  background-color: #fff;
  border-color: #faad14;
  color: #faad14;
}

/* 标签页样式 */
.detail-tabs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .detail-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .detail-title-left {
    width: 100%;
  }
  
  .detail-title-right {
    margin-left: 0;
    align-self: flex-end;
  }
} 