import { get, post, put, del } from '../api';

// 工作表相关类型定义
export interface WorksheetCreate {
  tenant_id: number;
  title: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published?: number;
  active?: number;
}

export interface WorksheetUpdate {
  title?: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published?: number;
  active?: number;
}

export interface WorksheetResponse {
  id: number;
  tenant_id: number;
  eid: number;
  title: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published: number;
  ctime: string;
  active: number;
}

export interface WorksheetListResponse {
  total: number;
  items: WorksheetResponse[];
}

export interface GetWorksheetsParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: string;
  title?: string;
  active?: number;
  published?: number;
  start_time?: string;
  end_time?: string;
}

// 获取工作表列表
export const getWorksheets = async (params: GetWorksheetsParams): Promise<WorksheetListResponse> => {
  const response = await get<WorksheetListResponse>('/sys/worksheet', params);
  return response;
};

// 创建工作表
export const createWorksheet = async (data: WorksheetCreate): Promise<WorksheetResponse> => {
  const response = await post<WorksheetResponse>('/sys/worksheet', data);
  return response;
};

// 获取工作表信息
export const getWorksheet = async (id: number): Promise<WorksheetResponse> => {
  const response = await get<WorksheetResponse>(`/sys/worksheet/${id}`);
  return response;
};

// 更新工作表
export const updateWorksheet = async (id: number, data: WorksheetUpdate): Promise<WorksheetResponse> => {
  const response = await put<WorksheetResponse>(`/sys/worksheet/${id}`, data);
  return response;
};

// 删除工作表
export const deleteWorksheet = async (id: number): Promise<{ message: string }> => {
  const response = await del<{ message: string }>(`/sys/worksheet/${id}`);
  return response;
}; 