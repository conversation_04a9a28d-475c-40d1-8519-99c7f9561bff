import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '../../../../services/system/auth';
import { useAuthStore, UserType } from '../../../../utils/authStore';
import { handleApiError } from '../../../../utils/errorHandler';

const SystemLoginForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { login: authLogin } = useAuthStore();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const onFinish = async (values: { username: string; password: string; remember: boolean }) => {
    try {
      setLoading(true);
      const user = await login(values.username, values.password);
      authLogin(user, UserType.SYSTEM);
      message.success('登录成功');
      navigate('/system', { replace: true });
    } catch (error: unknown) {
      // 特殊处理登录接口的400状态码错误
      let errorMessage = '登录失败，请检查用户名和密码';
      
      // 检查是否是400状态码的错误
      const apiError = error as {
        response?: {
          status?: number;
          data?: {
            detail?: string;
          };
        };
      };
      if (apiError?.response?.status === 400 && apiError?.response?.data?.detail) {
        errorMessage = apiError.response.data.detail;
      } else {
        // 其他错误使用统一的错误处理机制
        errorMessage = handleApiError(error, '登录失败，请检查用户名和密码');
      }
      
      message.error(errorMessage);
      // 重置密码字段，但保留用户名
      form.setFieldsValue({ password: '' });
      // 不进行页面跳转，保持在当前登录页面
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ width: '80%' }}>
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <h3>系统管理员登录</h3>
      </div>
      <Form
        name="system_login"
        form={form}
        initialValues={{ remember: true }}
        onFinish={onFinish}
        style={{ width: '100%' }}
      >
        <Form.Item
          name="username"
          rules={[{ required: true, message: '请输入用户名!' }]}
        >
          <Input prefix={<UserOutlined />} placeholder="用户名" />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: '请输入密码!' }]}
        >
          <Input.Password prefix={<LockOutlined />} placeholder="密码" />
        </Form.Item>
        <Form.Item>
          <Form.Item name="remember" valuePropName="checked" noStyle>
            <Checkbox>记住我</Checkbox>
          </Form.Item>

          <a className="login-form-forgot" href="#" onClick={(e) => e.preventDefault()}>
            忘记密码
          </a>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" className="login-form-button" loading={loading}>
            登录
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default SystemLoginForm; 