import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, DatePicker, Tag, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { 
  getAdmins, 
  deleteAdmin,
  updateAdmin,
  type SysAdminResponse 
} from '../../../services/system/admin';
import { showError, showSuccess } from '../../../utils/errorHandler';
import AdminForm from './AdminForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

// 角色映射
const roleMap: Record<number, { name: string; color: string }> = {
  0: { name: '超级管理员', color: 'red' },
  1: { name: '管理员', color: 'blue' },
};

const AdminManagement: React.FC = () => {
  const [admins, setAdmins] = useState<SysAdminResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentAdmin, setCurrentAdmin] = useState<SysAdminResponse | null>(null);
  const [searchUsername, setSearchUsername] = useState('');
  const [searchUsernameValue, setSearchUsernameValue] = useState(''); // 用于存储用户名输入框的当前值
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储姓名输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0); // 用于强制重新渲染表格组件
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 使用一个对象来存储所有列的排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc', // 默认按ID降序排序
    ctime: undefined
  });

  // 用于跟踪是否是首次渲染
  const isFirstRender = useRef(true);

  // 创建对搜索框和日期选择器的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchUsernameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const datePickerRef = useRef<any>(null);

  // 获取管理员列表
  const fetchAdmins = useCallback(async () => {
    try {
      setLoading(true);
      
      // 构建排序参数 - 只使用当前激活的排序字段
      let sortBy: string | undefined;
      let sortOrder: string | undefined;
      
      // 直接判断具体的排序字段，互斥处理
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getAdmins({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        username: searchUsername || undefined,
        name: searchName || undefined,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
      });
      
      // 直接使用API返回的数据，无需客户端过滤
      setAdmins(response.items);
      
      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取管理员列表失败');
    } finally {
      setLoading(false);
    }
  }, [searchUsername, searchName, sortOrders, isRecycleBin, dateRange, pagination.current, pagination.pageSize]);

  useEffect(() => {
    // 初次加载数据
    fetchAdmins();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 监听搜索条件变化，触发数据获取
  useEffect(() => {
    // 跳过组件初始挂载时的执行
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 手动触发数据获取
    fetchAdmins();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRecycleBin, searchUsername, searchName, dateRange, sortOrders]);

  // 处理用户名搜索
  const handleUsernameSearch = (value: string) => {
    setSearchUsername(value);
    setSearchUsernameValue(value);
  };

  // 处理用户名搜索输入框值变化，但不触发搜索
  const handleUsernameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchUsernameValue(e.target.value);
  };

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理日期范围变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      // 转换为ISO 8601格式，保持本地时区
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
  };

  // 处理添加管理员
  const handleAdd = () => {
    setCurrentAdmin(null);
    setModalVisible(true);
  };

  // 处理编辑管理员
  const handleEdit = (record: SysAdminResponse) => {
    setCurrentAdmin(record);
    setModalVisible(true);
  };

  // 处理禁用管理员（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = isRecycleBin ? '彻底删除' : '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个管理员吗？${isRecycleBin ? '此操作不可撤销。' : '禁用后可在回收站中恢复。'}`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (isRecycleBin) {
            // 在回收站模式下，执行真正的删除
            await deleteAdmin(id);
          } else {
            // 在正常模式下，将管理员禁用（设置active为0）
            await updateAdmin(id, { active: 0 });
          }
          showSuccess(`${actionText}成功`);
          fetchAdmins();
        } catch (error: unknown) {
          showError(error, `${actionText}管理员失败`);
        }
      },
    });
  };

  // 处理恢复管理员
  const handleRestore = async (adminId: number) => {
    try {
      await updateAdmin(adminId, { active: 1 });
      showSuccess('恢复成功');
      fetchAdmins();
    } catch (error) {
      showError(error, '恢复管理员失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchAdmins();
  };

  // 处理重置刷新按钮点击 - 重置所有筛选条件并刷新数据
  const handleRefresh = async () => {
    // 暂时禁用 useEffect 触发的自动获取数据
    isFirstRender.current = true;

    // 清空搜索框
    setSearchUsername('');
    setSearchUsernameValue('');
    setSearchName('');
    setSearchNameValue('');

    // 清空时间区间控件
    setDateRange(null);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    try {
      // 直接调用接口获取数据，使用重置后的参数
      setLoading(true);
      const response = await getAdmins({
        skip: 0,
        limit: 10,
        username: undefined, // 重置后的用户名搜索条件
        name: undefined,     // 重置后的姓名搜索条件
        active: isRecycleBin ? 0 : 1, // 根据当前模式传递正确的active参数进行服务端过滤
        sort_by: 'id',      // 默认ID排序
        sort_order: 'desc', // 默认降序
        start_time: undefined, // 重置后的开始时间
        end_time: undefined,   // 重置后的结束时间
      });
      
      // 直接使用API返回的数据，无需客户端过滤
      setAdmins(response.items);
      
      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      // 强制重新渲染表格组件，以重置筛选 UI
      setTableKey(prevKey => prevKey + 1);
    } catch (error) {
      showError(error, '获取管理员列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格排序变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableChange = (paginationConfig: any, _filters: Record<string, unknown>, sorter: unknown) => {
    // 处理分页变化
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    // 定义排序接口
    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    // 转换为单个sorter对象
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    // 重置所有排序状态
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    // 根据点击的列头设置对应的sortBy和sortOrder
    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        // 点击ID列头，sortBy=id，sortOrder为正序、逆序、取消排序轮询
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
        // order为undefined时表示取消排序，保持undefined
      } else if (field === 'ctime') {
        // 点击创建时间列头，sortBy=ctime，sortOrder也类似轮询
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
        // order为undefined时表示取消排序，保持undefined
      }
      
    }

    // 如果所有排序都被取消，则使用默认排序（ID 降序）
    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    // 更新排序状态
    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true, // 改为简单的单一排序
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username',
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        render: (role: number) => {
          const roleInfo = roleMap[role] || { name: '未知角色', color: 'default' };
          return <Tag color={roleInfo.color}>{roleInfo.name}</Tag>;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        render: (text: string) => new Date(text).toLocaleString(),
        sorter: true, // 改为简单的单一排序
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : undefined,
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        render: (active: number) => (
          <span style={{ color: active === 1 ? 'green' : 'red' }}>
            {active === 1 ? '启用' : '禁用'}
          </span>
        ),
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: SysAdminResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: SysAdminResponse) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '管理员管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "管理员回收站" : "管理员管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索用户名"
              allowClear
              onSearch={handleUsernameSearch}
              style={{ width: 160 }}
              ref={searchUsernameInputRef}
              value={searchUsernameValue}
              onChange={handleUsernameInputChange}
            />
            <Search
              placeholder="搜索姓名"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              ref={datePickerRef}
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {/* 添加管理员按钮只在非回收站模式下显示 */}
            {!isRecycleBin && (
              <Tooltip title="添加管理员">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey} // 添加 key 属性，使其在 key 变化时重新渲染
          columns={getColumns()}
          dataSource={admins}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: isRecycleBin ? '回收站中没有管理员' : '暂无数据'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      <AdminForm
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleFormSuccess}
        admin={currentAdmin}
      />
    </div>
  );
};

export default AdminManagement; 