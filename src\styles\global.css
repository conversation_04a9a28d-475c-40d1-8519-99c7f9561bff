/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  background-color: #f5f5f5;
}

#root {
  height: 100vh;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.ant-card-body {
  padding: 24px;
}

/* 表格样式优化 */
.ant-table {
  background: #fff;
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid #f5f5f5;
}

.ant-table-tbody > tr:hover > td {
  background: #fafafa;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.ant-btn-danger {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover {
  background: #ff7875;
  border-color: #ff7875;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 输入框样式优化 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 模态框样式优化 */
.ant-modal {
  border-radius: 8px;
}

.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.ant-modal-body {
  padding: 24px;
}

/* 分页样式优化 */
.ant-pagination {
  margin-top: 24px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-pagination-item-active a {
  color: #fff;
}

/* 工具提示样式 */
.ant-tooltip {
  font-size: 12px;
}

/* 开关样式 */
.ant-switch {
  border-radius: 12px;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 侧边栏样式优化 */
.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.ant-menu-dark {
  background: #001529;
}

.ant-menu-dark .ant-menu-item-selected {
  background: #1890ff !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background: rgba(24, 144, 255, 0.1);
}

/* 确保选中状态在悬停时保持高亮 */
.ant-menu-dark .ant-menu-item-selected:hover {
  background: #1890ff !important;
}

/* 头部样式优化 */
.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 10;
  position: relative;
}

/* 内容区域样式 */
.ant-layout-content {
  background: #f5f5f5;
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s;
}

.ant-spin-blur {
  opacity: 0.5;
  user-select: none;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #8c8c8c;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-form-item-explain-error {
  font-size: 12px;
}

/* 搜索框样式 */
.ant-input-search .ant-input-group-addon .ant-btn {
  border-radius: 0 6px 6px 0;
}

/* 状态指示器 */
.status-active {
  color: #52c41a;
  font-weight: 500;
}

.status-inactive {
  color: #ff4d4f;
  font-weight: 500;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
}

/* 页面标题 */
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24px;
}

/* 统计卡片 */
.stat-card {
  text-align: center;
  padding: 24px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-card .stat-label {
  font-size: 14px;
  opacity: 0.9;
} 