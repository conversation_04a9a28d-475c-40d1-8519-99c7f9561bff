import React, { useState, useMemo } from 'react';
import { Table, Button, Space, Tooltip } from 'antd';
import { EditOutlined, HolderOutlined } from '@ant-design/icons';
import '../../../styles/sortable-table.css';
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  restrictToVerticalAxis,
} from '@dnd-kit/modifiers';
import { type QuestionGuideResponse } from '../../../services/system/question';

// 可拖拽的表格行组件
interface DraggableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
  children: React.ReactNode;
}

const DraggableRow: React.FC<DraggableRowProps> = React.memo(({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transition: isDragging ? 'none' : (transition || 'transform 200ms cubic-bezier(0.645, 0.045, 0.355, 1)'),
    cursor: isDragging ? 'grabbing' : 'default',
    ...(isDragging ? { 
      opacity: 0.5,
      transform: CSS.Transform.toString(transform),
    } : {
      transform: CSS.Transform.toString(transform),
      transition: 'all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)',
    }),
  };

  // 将子元素转换为数组以便插入拖拽手柄
  const childrenArray = React.Children.toArray(children);
  
  // 在第一列后插入拖拽手柄列
  if (Array.isArray(childrenArray) && childrenArray.length > 0) {
    const firstCell = childrenArray[0] as React.ReactElement;
    const modifiedFirstCell = React.cloneElement(firstCell, {
      children: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span
            {...attributes}
            {...listeners}
            style={{
              cursor: 'grab',
              color: '#8c8c8c',
              fontSize: '14px',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '4px',
              transition: 'all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)',
              backgroundColor: 'transparent',
              width: '20px',
              height: '20px',
              border: '1px solid transparent',
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
            onMouseUp={() => {
              if (!document.body.classList.contains('dragging')) {
                document.body.style.cursor = 'default';
              }
            }}
            className="drag-handle"
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.classList.add('hover');
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.classList.remove('hover');
            }}
          >
            <HolderOutlined />
          </span>
          {firstCell.props.children}
        </div>
      )
    });
    
    childrenArray[0] = modifiedFirstCell;
  }

  return (
    <tr 
      {...props} 
      ref={setNodeRef} 
      style={style}
      className={isDragging ? 'dragging-row' : ''}
    >
      {childrenArray}
    </tr>
  );
});

// 排序表格组件的Props接口
interface SortableGuideTableProps {
  guides: QuestionGuideResponse[];
  loading: boolean;
  onGuidesChange: (guides: QuestionGuideResponse[]) => void;
  onEdit: (guide: QuestionGuideResponse) => void;
  onDelete: (guideId: number) => void;
}

const SortableGuideTable: React.FC<SortableGuideTableProps> = ({
  guides,
  loading,
  onGuidesChange,
  onEdit,
  onDelete,
}) => {
  // 拖拽覆盖层状态
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<QuestionGuideResponse | null>(null);
  const [overId, setOverId] = useState<string | null>(null);

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: (event) => {
        if (event.code === 'ArrowDown') {
          return { x: 0, y: 1 };
        }
        if (event.code === 'ArrowUp') {
          return { x: 0, y: -1 };
        }
        return undefined;
      },
    })
  );

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    
    const draggedGuide = guides.find(item => item.id.toString() === active.id);
    setDraggedItem(draggedGuide || null);
    
    document.body.classList.add('dragging');
    document.body.style.cursor = 'grabbing';
  };

  // 处理拖拽悬停
  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    setOverId(over ? over.id as string : null);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setDraggedItem(null);
    setOverId(null);
    
    document.body.classList.remove('dragging');
    document.body.style.cursor = 'default';

    if (over && active.id !== over.id) {
      const oldIndex = guides.findIndex(item => item.id.toString() === active.id.toString());
      const newIndex = guides.findIndex(item => item.id.toString() === over.id.toString());
      
      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
        const newGuides = arrayMove(guides, oldIndex, newIndex);
        onGuidesChange(newGuides);
      }
    }
  };

  // 表格列定义
  const columns = useMemo(() => [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: unknown, record: QuestionGuideResponse) => (
        <Space size="middle">
          <Tooltip title="排序模式下不可编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              disabled={true}
              onClick={() => onEdit(record)}
            />
          </Tooltip>
        </Space>
      ),
    } as never,
  ], [onEdit, onDelete]);

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext
          items={guides.map(item => item.id.toString())}
          strategy={verticalListSortingStrategy}
        >
          <Table
            columns={columns}
            dataSource={guides}
            rowKey={(record) => record.id.toString()}
            loading={loading}
            pagination={false}
            virtual={guides.length > 100}
            scroll={guides.length > 100 ? { y: 400 } : undefined}
            locale={{
              emptyText: '暂无问题指南数据'
            }}
            components={{
              body: {
                row: DraggableRow,
              },
            }}
            showSorterTooltip={false}
            size="middle"
            rowClassName={(record) => {
              const recordIdStr = record.id.toString();
              const overIdStr = overId ? overId.toString() : null;
              const activeIdStr = activeId ? activeId.toString() : null;
              
              const isDropTarget = overIdStr && recordIdStr === overIdStr && activeIdStr && activeIdStr !== overIdStr;
              
              if (isDropTarget) {
                return 'drop-indicator';
              }
              return '';
            }}
          />
        </SortableContext>
        
        <DragOverlay>
          {activeId && draggedItem ? (
            <table style={{ 
              backgroundColor: '#ffffff',
              boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)',
              borderRadius: '6px',
              border: '1px solid #d9d9d9',
              minWidth: '600px',
            }}>
              <tbody>
                <tr style={{ backgroundColor: '#ffffff' }}>
                  <td style={{ padding: '12px 16px', border: 'none', display: 'flex', alignItems: 'center', gap: 8 }}>
                    <HolderOutlined style={{ color: '#8c8c8c' }} />
                    {draggedItem.id}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none', fontWeight: 500 }}>
                    {draggedItem.title}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none', color: '#595959' }}>
                    {draggedItem.details && draggedItem.details.length > 50 
                      ? `${draggedItem.details.slice(0, 50)}...` 
                      : draggedItem.details || '-'}
                  </td>
                  <td style={{ padding: '12px 16px', border: 'none' }}>
                    <Space size="middle">
                      <Button
                        type="primary"
                        icon={<EditOutlined />}
                        size="small"
                        disabled={true}
                      />
                    </Space>
                  </td>
                </tr>
              </tbody>
            </table>
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  );
};

export default SortableGuideTable; 