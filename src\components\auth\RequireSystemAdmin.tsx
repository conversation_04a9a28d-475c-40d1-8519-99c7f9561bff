import React from 'react';
import { Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import { useAuthStore } from '../../utils/authStore';

interface RequireSystemAdminProps {
  children: React.ReactNode;
}

const RequireSystemAdmin: React.FC<RequireSystemAdminProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuthStore();

  // 如果未认证，重定向到登录页
  if (!isAuthenticated || !user) {
    return <Navigate to="/system/login" replace />;
  }

  // 如果不是超级管理员，显示无权限页面
  if (user.role !== 0) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面。"
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default RequireSystemAdmin; 