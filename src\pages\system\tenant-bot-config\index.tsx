import React, { useState, useEffect, useRef } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Popconfirm,
  Card,
  Tooltip,
  Select,
  Breadcrumb,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getTenantBotConfigs,
  createTenantBotConfig,
  updateTenantBotConfig,
  deleteTenantBotConfig,
  type TenantBotConfig,
  type TenantBotConfigCreate,
  type TenantBotConfigUpdate,
} from '../../../services/system/tenant-bot-config';
import { getTenants, type TenantListResponse } from '../../../services/system/tenant';
import { showError, showSuccess } from '../../../utils/errorHandler';
import TenantBotConfigForm from './TenantBotConfigForm';
import { Link } from 'react-router-dom';

const { Search } = Input;

const TenantBotConfigManagement: React.FC = () => {
  const [configs, setConfigs] = useState<TenantBotConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [keywordValue, setKeywordValue] = useState('');
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<TenantBotConfig | null>(null);
  const [tenants, setTenants] = useState<TenantListResponse['items']>([]);
  const [tenantsLoading, setTenantsLoading] = useState(false);
  
  // 防抖搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 获取租户列表（支持搜索）
  const fetchTenants = async (searchKeyword?: string) => {
    setTenantsLoading(true);
    try {
      const response = await getTenants({
        skip: 0,
        limit: 100,
        keyword: searchKeyword,
      });
      setTenants(response.items);
      return response.items;
    } catch (error) {
      showError(error, '获取租户列表失败');
      return [];
    } finally {
      setTenantsLoading(false);
    }
  };

  // 处理租户搜索（防抖）
  const handleTenantSearch = (value: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索值为空，清空列表
    if (!value || value.trim() === '') {
      setTenants([]);
      return;
    }

    // 设置新的定时器，0.5秒后执行搜索
    searchTimeoutRef.current = setTimeout(() => {
      fetchTenants(value.trim());
    }, 500);
  };

  // 获取设置列表
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await getTenantBotConfigs({
        skip: (current - 1) * pageSize,
        limit: pageSize,
        keyword: keyword || undefined,
      });
      
      // 如果选择了租户，进行过滤
      let filteredConfigs = response.items;
      if (selectedTenantId !== undefined) {
        filteredConfigs = response.items.filter(config => config.tenant_id === selectedTenantId);
      }
      
      setConfigs(filteredConfigs);
      setTotal(selectedTenantId !== undefined ? filteredConfigs.length : response.total);
    } catch (error) {
      showError(error, '获取设置列表失败');
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    fetchConfigs();
  }, [current, pageSize, keyword, selectedTenantId]);



  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // 搜索设置
  const handleSearch = (value: string) => {
    setKeyword(value);
    setKeywordValue(value);
    setCurrent(1);
  };

  // 处理搜索输入框值变化，但不触发搜索
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeywordValue(e.target.value);
  };

  // 处理租户筛选
  const handleTenantChange = (value: number | undefined) => {
    setSelectedTenantId(value);
    setCurrent(1);
  };

  // 新增设置
  const handleAdd = () => {
    setEditingConfig(null);
    setIsModalVisible(true);
  };

  // 编辑设置
  const handleEdit = (config: TenantBotConfig) => {
    setEditingConfig(config);
    setIsModalVisible(true);
  };

  // 删除设置
  const handleDelete = async (tenantId: number, key: string) => {
    try {
      await deleteTenantBotConfig(tenantId, key);
      showSuccess('删除成功');
      fetchConfigs();
    } catch (error) {
      showError(error, '删除失败');
    }
  };

  // 处理重置刷新按钮点击
  const handleRefresh = async () => {
    // 清空搜索条件
    setKeyword('');
    setKeywordValue('');
    setSelectedTenantId(undefined);
    setTenants([]); // 清空租户列表
    
    // 重置分页状态
    setCurrent(1);
    setPageSize(10);

    try {
      setLoading(true);
      const response = await getTenantBotConfigs({
        skip: 0,
        limit: 10,
        keyword: undefined,
      });
      setConfigs(response.items);
      setTotal(response.total);
    } catch (error) {
      showError(error, '刷新数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: {
    tenant_id: number;
    key: string;
    bid: number;
    notes?: string;
  }) => {
    try {
      if (editingConfig) {
        // 编辑设置
        const updateData: TenantBotConfigUpdate = {
          tenant_id: values.tenant_id,
          key: values.key,
          bid: values.bid,
          notes: values.notes,
        };
        await updateTenantBotConfig(editingConfig.tenant_id, editingConfig.key, updateData);
        showSuccess('更新成功');
      } else {
        // 新增设置
        const createData: TenantBotConfigCreate = {
          tenant_id: values.tenant_id,
          key: values.key,
          bid: values.bid,
          notes: values.notes,
        };
        await createTenantBotConfig(createData);
        showSuccess('创建成功');
      }

      setIsModalVisible(false);
      fetchConfigs();
    } catch (error) {
      showError(error, editingConfig ? '更新失败' : '创建失败');
    }
  };



  // 根据机器人ID获取机器人名称
  const getBotName = (config: TenantBotConfig) => {
    return config.bot_name;
  };

  const columns: ColumnsType<TenantBotConfig> = [
    {
      title: '所属租户',
      dataIndex: 'tenant_name',
      key: 'tenant_name',
      width: 150,
      render: (tenantName: string, record: TenantBotConfig) => tenantName || `ID: ${record.tenant_id}`,
    },
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      width: 200,
    },
    {
      title: '机器人名称',
      dataIndex: 'bid',
      key: 'botName',
      width: 150,
      render: (bid: number, record: TenantBotConfig) => getBotName(record),
    },
    {
      title: '机器人ID',
      dataIndex: 'bid',
      key: 'bid',
      width: 100,
      render: (bid: number) => bid,
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个设置吗？"
            onConfirm={() => handleDelete(record.tenant_id, record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '租户机器人设置管理',
          },
        ]}
      />

      <Card
        title="租户机器人设置管理"
        extra={
          <Space>
            <Select
              placeholder="选择租户"
              allowClear
              showSearch
              loading={tenantsLoading}
              filterOption={false}
              onSearch={handleTenantSearch}
              style={{ width: 160 }}
              value={selectedTenantId}
              onChange={handleTenantChange}
              notFoundContent={tenantsLoading ? '搜索中...' : tenants.length === 0 ? '请输入租户名称' : '暂无数据'}
            >
              {tenants.map(tenant => (
                <Select.Option key={tenant.id} value={tenant.id}>
                  {tenant.name}（ID: {tenant.id}）
                </Select.Option>
              ))}
            </Select>
            
            <Search
              placeholder="搜索Key"
              allowClear
              value={keywordValue}
              onChange={handleSearchInputChange}
              onSearch={handleSearch}
              style={{ width: 160 }}
            />
            
            <Tooltip title="添加租户机器人设置">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              />
            </Tooltip>
            
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey={(record) => `${record.tenant_id}-${record.key}`}
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
          locale={{
            emptyText: '暂无数据'
          }}
        />
      </Card>

      <Modal
        title={editingConfig ? '编辑租户机器人设置' : '添加租户机器人设置'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <TenantBotConfigForm
          editingConfig={editingConfig}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalVisible(false)}
        />
      </Modal>
    </div>
  );
};

export default TenantBotConfigManagement; 