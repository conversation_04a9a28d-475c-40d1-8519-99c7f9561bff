import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Row, Col, InputNumber } from 'antd';
import { 
  createWorksheet, 
  updateWorksheet, 
  type WorksheetResponse,
  type WorksheetCreate,
  type WorksheetUpdate 
} from '../../../services/system/worksheet';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface WorksheetFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  worksheet?: WorksheetResponse | null;
  tenantId: number | null;
}

const WorksheetForm: React.FC<WorksheetFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  worksheet,
  tenantId
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEditing = !!worksheet;

  useEffect(() => {
    if (visible) {
      if (worksheet) {
        // 编辑模式，填充表单数据
        form.setFieldsValue({
          title: worksheet.title,
          intro: worksheet.intro,
          duration: worksheet.duration,
          version: worksheet.version,
          notes: worksheet.notes,
        });
      } else {
        // 新增模式，重置表单
        form.resetFields();
      }
    }
  }, [visible, worksheet, form]);

  const handleSubmit = async () => {
    if (!tenantId) {
      showError('请先选择租户', '操作失败');
      return;
    }

    try {
      const values = await form.validateFields();
      setLoading(true);

      const formData = {
        ...values,
        tenant_id: tenantId,
        type: 0, // 默认类型：填空题
        published: 0, // 默认未发布
        active: 1, // 默认启用
      };

      if (isEditing && worksheet) {
        // 编辑作业单
        const updateData: WorksheetUpdate = {
          title: formData.title,
          intro: formData.intro,
          duration: formData.duration,
          version: formData.version,
          notes: formData.notes,
        };
        await updateWorksheet(worksheet.id, updateData);
        showSuccess('作业单更新成功');
      } else {
        // 创建作业单
        const createData: WorksheetCreate = {
          title: formData.title,
          intro: formData.intro,
          duration: formData.duration,
          version: formData.version,
          notes: formData.notes,
          type: formData.type,
          published: formData.published,
          active: formData.active,
          tenant_id: formData.tenant_id,
        };
        await createWorksheet(createData);
        showSuccess('作业单创建成功');
      }

      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误
        return;
      }
      showError(error, isEditing ? '更新作业单失败' : '创建作业单失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEditing ? '编辑作业单' : '添加作业单'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      destroyOnHidden
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="标题"
          name="title"
          rules={[
            { required: true, message: '请输入标题' },
            { max: 200, message: '标题长度不能超过200字符' }
          ]}
        >
          <Input placeholder="请输入作业单标题" />
        </Form.Item>

        <Form.Item
          label="简介"
          name="intro"
          rules={[
            { max: 1000, message: '简介长度不能超过1000字符' }
          ]}
        >
          <TextArea 
            rows={3} 
            placeholder="请输入作业单简介（可选）" 
            showCount 
            maxLength={1000}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="时长（分钟）"
              name="duration"
              rules={[
                { type: 'number', min: 1, message: '时长必须大于0分钟' }
              ]}
            >
              <InputNumber 
                placeholder="预估练习时长" 
                style={{ width: '100%' }}
                min={1}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="版本"
              name="version"
              rules={[
                { max: 50, message: '版本长度不能超过50字符' }
              ]}
            >
              <Input 
                placeholder="请输入作业单版本（可选）" 
                showCount 
                maxLength={50}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="备注"
          name="notes"
          rules={[
            { max: 1000, message: '备注长度不能超过1000字符' }
          ]}
        >
          <TextArea 
            rows={3} 
            placeholder="请输入作业单备注（可选）" 
            showCount 
            maxLength={1000}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WorksheetForm; 