import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Row, Col } from 'antd';
import { 
  createBot, 
  updateBot,
  type Bot,
  type BotCreate,
  type BotUpdate 
} from '../../../services/system/bot';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface BotFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  bot: Bot | null;
}

const BotForm: React.FC<BotFormProps> = ({ visible, onCancel, onSuccess, bot }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (bot) {
        form.setFieldsValue({
          name: bot.name,
          api_endpoint: bot.api_endpoint,
          api_key: bot.api_key,
          notes: bot.notes,
          sys_prompt: bot.sys_prompt,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, bot, form]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (bot) {
        const updateData: BotUpdate = {
          name: values.name,
          api_endpoint: values.api_endpoint,
          api_key: values.api_key,
          notes: values.notes,
          sys_prompt: values.sys_prompt,
        };
        
        await updateBot(bot.id, updateData);
        showSuccess('更新机器人成功');
      } else {
        const createData: BotCreate = {
          name: values.name,
          api_endpoint: values.api_endpoint,
          api_key: values.api_key,
          notes: values.notes,
          sys_prompt: values.sys_prompt,
        };
        await createBot(createData);
        showSuccess('创建机器人成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={bot ? '编辑机器人' : '添加机器人'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
      width={700}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="名称"
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="notes"
              label="备注"
            >
              <Input placeholder="请输入备注" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="api_endpoint"
          label="API路径"
          rules={[{ required: true, message: '请输入API路径' }]}
        >
          <Input placeholder="请输入API路径" />
        </Form.Item>

        <Form.Item
          name="api_key"
          label="API密钥"
          rules={[{ required: true, message: '请输入API密钥' }]}
        >
          <Input.Password placeholder="请输入API密钥" />
        </Form.Item>

        <Form.Item
          name="sys_prompt"
          label="系统提示词模板"
          rules={[{ required: true, message: '请输入系统提示词模板' }]}
        >
          <TextArea rows={6} placeholder="请输入系统提示词模板" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BotForm; 