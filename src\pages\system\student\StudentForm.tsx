import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select } from 'antd';
import { 
  createStudent, 
  updateStudent,
  type StudentResponse,
  type StudentCreate,
  type StudentUpdate 
} from '../../../services/system/student';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { encryptPassword } from '../../../utils/crypto';

interface StudentFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  student: StudentResponse | null;
  tenantId: number;
}

const StudentForm: React.FC<StudentFormProps> = ({ visible, onCancel, onSuccess, student, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (student) {
        form.setFieldsValue({
          name: student.name,
          gender: student.gender,
          notes: student.notes,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, student, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (student) {
        const updateData: StudentUpdate = {
          name: values.name,
          gender: values.gender,
          notes: values.notes,
        };
        
        if (values.password && values.password.trim() !== '') {
          updateData.password = encryptPassword(values.password);
        }
        
        await updateStudent(student.id, updateData);
        showSuccess('更新学员成功');
      } else {
        const createData: StudentCreate = {
          tenant_id: tenantId,
          username: values.username,
          password: encryptPassword(values.password),
          name: values.name,
          gender: values.gender,
          notes: values.notes,
        };
        await createStudent(createData);
        showSuccess('创建学员成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={student ? '编辑学员' : '添加学员'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ gender: 0 }}
      >
        {!student && (
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
        )}

        <Form.Item
          name="name"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>

        <Form.Item
          name="gender"
          label="性别"
          rules={[{ required: true, message: '请选择性别' }]}
        >
          <Select placeholder="请选择性别">
            <Select.Option value={0}>未知</Select.Option>
            <Select.Option value={1}>男</Select.Option>
            <Select.Option value={2}>女</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <Input.TextArea 
            placeholder="请输入备注信息" 
            rows={3}
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: !student, message: '请输入密码' }]}
        >
          <Input.Password placeholder={student ? "留空则不修改密码" : "请输入密码"} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default StudentForm; 