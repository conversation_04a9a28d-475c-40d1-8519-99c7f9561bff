/* 粒子动画效果 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(100, 150, 255, 0.5);
  box-shadow: 0 0 15px rgba(100, 150, 255, 0.7);
  pointer-events: none;
}

.particle:nth-child(1) {
  width: 12px;
  height: 12px;
  top: 10%;
  left: 20%;
  animation: float 25s infinite ease-in-out;
}

.particle:nth-child(2) {
  width: 16px;
  height: 16px;
  top: 30%;
  left: 40%;
  animation: float 20s infinite ease-in-out;
  animation-delay: -2s;
}

.particle:nth-child(3) {
  width: 10px;
  height: 10px;
  top: 60%;
  left: 10%;
  animation: float 22s infinite ease-in-out;
  animation-delay: -5s;
}

.particle:nth-child(4) {
  width: 14px;
  height: 14px;
  top: 80%;
  left: 70%;
  animation: float 18s infinite ease-in-out;
  animation-delay: -7s;
}

.particle:nth-child(5) {
  width: 8px;
  height: 8px;
  top: 40%;
  left: 80%;
  animation: float 24s infinite ease-in-out;
  animation-delay: -3s;
}

.particle:nth-child(6) {
  width: 18px;
  height: 18px;
  top: 20%;
  left: 60%;
  animation: float 21s infinite ease-in-out;
  animation-delay: -8s;
}

.particle:nth-child(7) {
  width: 10px;
  height: 10px;
  top: 70%;
  left: 30%;
  animation: float 23s infinite ease-in-out;
  animation-delay: -1s;
}

.particle:nth-child(8) {
  width: 12px;
  height: 12px;
  top: 50%;
  left: 50%;
  animation: float 19s infinite ease-in-out;
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-70px) translateX(35px);
    opacity: 1;
  }
  50% {
    transform: translateY(-35px) translateX(-35px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(70px) translateX(20px);
    opacity: 1;
  }
} 