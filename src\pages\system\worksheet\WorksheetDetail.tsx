import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Form,
  Input, 
  Button, 
  Space, 
  Breadcrumb, 
  Spin, 
  Row,
  Col,
  Typography,
  theme,
  Tabs,
  message,
  Modal,
  InputNumber,
  Select,
  Tooltip
} from 'antd';
import { 
  HomeOutlined, 
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  FileTextOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, Link, useBlocker } from 'react-router-dom';
import { 
  getWorksheet, 
  updateWorksheet,
  type WorksheetResponse
} from '../../../services/system/worksheet';
import { showError } from '../../../utils/errorHandler';
import WorksheetBackgroundTab, { type WorksheetBackgroundTabRef } from './WorksheetBackgroundTab';
import WorksheetUnitsTab from './WorksheetUnitsTab';
import styles from '../../../components/RichTextEditor.module.css';
import '../../../styles/detail-page.css';

const { Title, Text } = Typography;
const { Option } = Select;

const WorksheetDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [worksheet, setWorksheet] = useState<WorksheetResponse | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const { token } = theme.useToken();
  const worksheetBackgroundTabRef = useRef<WorksheetBackgroundTabRef>(null);
  
  // 内联编辑状态
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingIntro, setEditingIntro] = useState(false);
  const [editingDuration, setEditingDuration] = useState(false);
  const [editingVersion, setEditingVersion] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [editingPublished, setEditingPublished] = useState(false);
  const [tempTitle, setTempTitle] = useState('');
  const [tempIntro, setTempIntro] = useState('');
  const [tempDuration, setTempDuration] = useState<number | undefined>(undefined);
  const [tempVersion, setTempVersion] = useState('');
  const [tempNotes, setTempNotes] = useState('');
  const [tempPublished, setTempPublished] = useState<number>(0);
  const [activeTab, setActiveTab] = useState('background');
  
  // 跟踪已访问的tab
  const [visitedTabs, setVisitedTabs] = useState<Set<string>>(new Set(['background']));
  
  // 确认对话框状态
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // 获取作业单详情
  const fetchWorksheet = useCallback(async () => {
    if (!id) return;
    
    try {
      setInitialLoading(true);
      const response = await getWorksheet(parseInt(id));
      setWorksheet(response);
      form.setFieldsValue({
        title: response.title,
        intro: response.intro,
        bgtext: response.bgtext,
        duration: response.duration,
        version: response.version,
        notes: response.notes,
        published: response.published,
      });
      setTempTitle(response.title);
      setTempIntro(response.intro || '');
      setTempDuration(response.duration);
      setTempVersion(response.version || '');
      setTempNotes(response.notes || '');
      setTempPublished(response.published);
    } catch (error) {
      showError(error, '获取作业单详情失败');
      navigate('/system/worksheet');
    } finally {
      setInitialLoading(false);
    }
  }, [id, form, navigate]);

  // 保存标题
  const handleSaveTitle = async () => {
    if (!worksheet || !tempTitle.trim()) return;
    
    try {
      await updateWorksheet(worksheet.id, { title: tempTitle.trim() });
      setWorksheet({ ...worksheet, title: tempTitle.trim() });
      setEditingTitle(false);
      message.success('标题更新成功');
    } catch (error) {
      showError(error, '更新标题失败');
    }
  };

  // 保存简介
  const handleSaveIntro = async () => {
    if (!worksheet) return;
    
    try {
      await updateWorksheet(worksheet.id, { intro: tempIntro });
      setWorksheet({ ...worksheet, intro: tempIntro });
      setEditingIntro(false);
      message.success('简介更新成功');
    } catch (error) {
      showError(error, '更新简介失败');
    }
  };

  // 保存练习时长
  const handleSaveDuration = async () => {
    if (!worksheet) return;
    
    try {
      await updateWorksheet(worksheet.id, { duration: tempDuration });
      setWorksheet({ ...worksheet, duration: tempDuration });
      setEditingDuration(false);
      message.success('练习时长更新成功');
    } catch (error) {
      showError(error, '更新练习时长失败');
    }
  };

  // 保存版本
  const handleSaveVersion = async () => {
    if (!worksheet) return;
    
    try {
      await updateWorksheet(worksheet.id, { version: tempVersion });
      setWorksheet({ ...worksheet, version: tempVersion });
      setEditingVersion(false);
      message.success('版本更新成功');
    } catch (error) {
      showError(error, '更新版本失败');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!worksheet) return;
    
    try {
      await updateWorksheet(worksheet.id, { notes: tempNotes });
      setWorksheet({ ...worksheet, notes: tempNotes });
      setEditingNotes(false);
      message.success('备注更新成功');
    } catch (error) {
      showError(error, '更新备注失败');
    }
  };

  // 保存发布状态
  const handleSavePublished = async () => {
    if (!worksheet) return;
    
    try {
      await updateWorksheet(worksheet.id, { published: tempPublished });
      setWorksheet({ ...worksheet, published: tempPublished });
      setEditingPublished(false);
      message.success('发布状态更新成功');
    } catch (error) {
      showError(error, '更新发布状态失败');
    }
  };

  // 开始编辑各字段
  const startEditTitle = () => {
    setTempTitle(worksheet?.title || '');
    setEditingTitle(true);
  };

  const startEditIntro = () => {
    setTempIntro(worksheet?.intro || '');
    setEditingIntro(true);
  };

  const startEditDuration = () => {
    setTempDuration(worksheet?.duration);
    setEditingDuration(true);
  };

  const startEditVersion = () => {
    setTempVersion(worksheet?.version || '');
    setEditingVersion(true);
  };

  const startEditNotes = () => {
    setTempNotes(worksheet?.notes || '');
    setEditingNotes(true);
  };

  const startEditPublished = () => {
    setTempPublished(worksheet?.published || 0);
    setEditingPublished(true);
  };

  // 取消编辑
  const cancelEditTitle = () => {
    setTempTitle(worksheet?.title || '');
    setEditingTitle(false);
  };

  const cancelEditIntro = () => {
    setTempIntro(worksheet?.intro || '');
    setEditingIntro(false);
  };

  const cancelEditDuration = () => {
    setTempDuration(worksheet?.duration);
    setEditingDuration(false);
  };

  const cancelEditVersion = () => {
    setTempVersion(worksheet?.version || '');
    setEditingVersion(false);
  };

  const cancelEditNotes = () => {
    setTempNotes(worksheet?.notes || '');
    setEditingNotes(false);
  };

  const cancelEditPublished = () => {
    setTempPublished(worksheet?.published || 0);
    setEditingPublished(false);
  };

  // 处理背景介绍保存成功
  const handleBgtextSaveSuccess = useCallback((updatedBgtext: string) => {
    if (worksheet) {
      setWorksheet({ ...worksheet, bgtext: updatedBgtext });
    }
  }, [worksheet]);

  // 检查是否有未保存的更改
  const checkUnsavedChanges = () => {
    return worksheetBackgroundTabRef.current?.hasUnsavedChanges() || false;
  };

  // 显示确认对话框
  const showConfirmModal = (action: () => void) => {
    if (checkUnsavedChanges()) {
      setPendingAction(() => action);
      setShowUnsavedModal(true);
    } else {
      action();
    }
  };

  // 处理保存并继续
  const handleSaveAndContinue = async () => {
    try {
      await worksheetBackgroundTabRef.current?.saveContent();
      setShowUnsavedModal(false);
      if (pendingAction) {
        pendingAction();
        setPendingAction(null);
      }
    } catch {
      // 保存失败，不执行待定操作
    }
  };

  // 处理不保存直接继续
  const handleDiscardAndContinue = () => {
    // 恢复到初始内容
    worksheetBackgroundTabRef.current?.resetToInitial();
    setShowUnsavedModal(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setShowUnsavedModal(false);
    setPendingAction(null);
  };

  // 使用路由阻塞器
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      currentLocation.pathname !== nextLocation.pathname && checkUnsavedChanges()
  );

  // 处理tab切换
  const handleTabChange = async (tabKey: string) => {
    const performTabChange = async () => {
      setActiveTab(tabKey);
      
      if (!visitedTabs.has(tabKey)) {
        setVisitedTabs(prev => new Set(prev).add(tabKey));
      }
    };

    if (activeTab === 'background') {
      // 从背景介绍tab切换出去时检查未保存更改
      showConfirmModal(performTabChange);
    } else {
      performTabChange();
    }
  };



  useEffect(() => {
    fetchWorksheet();
  }, [fetchWorksheet]);

  // 监听页面卸载事件
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (checkUnsavedChanges()) {
        e.preventDefault();
        e.returnValue = '您有未保存的内容，确定要离开吗？';
        return '您有未保存的内容，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // 处理路由阻塞
  useEffect(() => {
    if (blocker.state === 'blocked') {
      showConfirmModal(() => {
        blocker.proceed();
      });
    }
  }, [blocker]);

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!worksheet) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Text type="secondary">作业单不存在</Text>
      </div>
    );
  }

  return (
    <div className="detail-page-container">
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 8 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: (
              <Link to="/system/worksheet">
                作业单管理
              </Link>
            ),
          },
          {
            title: '作业单详情',
          },
        ]}
      />

      <div style={{ backgroundColor: token.colorBgContainer, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 头部区域 */}
        <div className="detail-page-header" style={{ borderBottom: `1px solid ${token.colorBorderSecondary}` }}>
          {/* 作业单标题和操作区 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
            <div style={{ flex: 1, minWidth: '300px' }}>
              {/* 作业单标题、发布状态、返回按钮、创建时间 */}
              <div className="detail-title-row" style={{ alignItems: 'center' }}>
                {editingTitle ? (
                  <div className="detail-title-left" style={{ alignItems: 'center' }}>
                    <Input
                      value={tempTitle}
                      onChange={(e) => setTempTitle(e.target.value)}
                      className="detail-title-input"
                      size="large"
                      onPressEnter={handleSaveTitle}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditTitle();
                        }
                      }}
                      autoFocus
                    />
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<CheckOutlined />}
                        onClick={handleSaveTitle}
                      />
                      <Button
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={cancelEditTitle}
                      />
                    </Space>
                  </div>
                ) : (
                  <div className="detail-title-left">
                    <Title 
                      level={3} 
                      className="detail-title"
                      onClick={startEditTitle}
                    >
                      {worksheet?.title || '加载中...'}
                    </Title>
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      size="small"
                      onClick={startEditTitle}
                      style={{ color: token.colorTextSecondary }}
                    />
                  </div>
                )}
                
                {/* 右侧：创建时间、发布状态 */}
                <div className="detail-title-right">
                  {/* 创建时间 */}
                  <Text 
                    type="secondary" 
                    className="detail-create-time"
                  >
                    创建于：{worksheet?.ctime ? new Date(worksheet.ctime).toLocaleString() : '未知'}
                  </Text>
                  
                  {/* 发布状态 */}
                  {editingPublished ? (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <Select
                        value={tempPublished}
                        onChange={setTempPublished}
                        size="small"
                        style={{ width: '80px' }}
                        autoFocus
                      >
                        <Option value={0}>草稿</Option>
                        <Option value={1}>已发布</Option>
                      </Select>
                      <Button
                        type="primary"
                        size="small"
                        icon={<CheckOutlined />}
                        onClick={handleSavePublished}
                      />
                      <Button
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={cancelEditPublished}
                      />
                    </div>
                  ) : (
                    <Button
                      type={worksheet?.published === 1 ? "primary" : "default"}
                      size="small"
                      onClick={startEditPublished}
                      className={`detail-publish-btn ${worksheet?.published === 1 ? 'published' : 'draft'}`}
                    >
                      {worksheet?.published === 1 ? '已发布' : '草稿'}
                    </Button>
                  )}
                </div>
              </div>
              
              {/* 字段信息区域 */}
              <Row gutter={[16, 12]}>
                {/* 简介、时长、版本、备注 */}
                <Col span={24}>
                  <Row gutter={[16, 12]}>
                    {/* 简介 */}
                    <Col span={10}>
                      <div className="detail-field-row">
                        <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                          简介
                        </Text>
                        {editingIntro ? (
                          <div className="detail-edit-container">
                            <Input
                              value={tempIntro}
                              onChange={(e) => setTempIntro(e.target.value)}
                              placeholder="添加简介..."
                              className="detail-edit-input"
                              autoFocus
                              onPressEnter={handleSaveIntro}
                              onKeyDown={(e) => {
                                if (e.key === 'Escape') {
                                  cancelEditIntro();
                                }
                              }}
                            />
                            <Button
                              type="primary"
                              size="small"
                              icon={<CheckOutlined />}
                              onClick={handleSaveIntro}
                            />
                            <Button
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={cancelEditIntro}
                            />
                          </div>
                        ) : (
                          <Tooltip title={worksheet?.intro || '（暂无）'} placement="top">
                            <Text 
                              className={`detail-field-content ${worksheet?.intro ? 'has-value' : 'empty'}`}
                              style={{
                                color: worksheet?.intro ? token.colorText : token.colorTextTertiary,
                              }}
                              onClick={startEditIntro}
                            >
                              {worksheet?.intro || '（暂无）'}
                            </Text>
                          </Tooltip>
                        )}
                      </div>
                    </Col>

                    {/* 时长 */}
                    <Col span={3}>
                      <div className="detail-field-row">
                        <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                          时长
                        </Text>
                        {editingDuration ? (
                          <div className="detail-edit-container" style={{ position: 'relative' }}>
                            <InputNumber
                              value={tempDuration}
                              onChange={(value) => setTempDuration(value || undefined)}
                              placeholder="分钟"
                              min={0}
                              style={{
                                width: '60px',
                                borderRadius: '6px',
                                fontSize: '14px',
                                backgroundColor: '#fafafa',
                                border: '1px solid #d9d9d9'
                              }}
                              autoFocus
                              onPressEnter={handleSaveDuration}
                              onKeyDown={(e) => {
                                if (e.key === 'Escape') {
                                  cancelEditDuration();
                                }
                              }}
                            />
                            <Button
                              type="primary"
                              size="small"
                              icon={<CheckOutlined />}
                              onClick={handleSaveDuration}
                              style={{ marginLeft: '4px', minWidth: '24px', padding: '0 4px' }}
                            />
                            <Button
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={cancelEditDuration}
                              style={{ marginLeft: '2px', minWidth: '24px', padding: '0 4px' }}
                            />
                          </div>
                        ) : (
                          <Tooltip title={worksheet?.duration ? `${worksheet.duration} 分钟` : '（暂无）'} placement="top">
                            <Text 
                              className={`detail-field-content ${worksheet?.duration ? 'has-value' : 'empty'}`}
                              style={{
                                color: worksheet?.duration ? token.colorText : token.colorTextTertiary,
                              }}
                              onClick={startEditDuration}
                            >
                              {worksheet?.duration ? `${worksheet.duration} 分钟` : '（暂无）'}
                            </Text>
                          </Tooltip>
                        )}
                      </div>
                    </Col>

                    {/* 版本 */}
                    <Col span={4}>
                      <div className="detail-field-row">
                        <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                          版本
                        </Text>
                        {editingVersion ? (
                          <div className="detail-edit-container">
                            <Input
                              value={tempVersion}
                              onChange={(e) => setTempVersion(e.target.value)}
                              placeholder="添加版本..."
                              className="detail-edit-input"
                              autoFocus
                              onPressEnter={handleSaveVersion}
                              onKeyDown={(e) => {
                                if (e.key === 'Escape') {
                                  cancelEditVersion();
                                }
                              }}
                            />
                            <Button
                              type="primary"
                              size="small"
                              icon={<CheckOutlined />}
                              onClick={handleSaveVersion}
                            />
                            <Button
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={cancelEditVersion}
                            />
                          </div>
                        ) : (
                          <Tooltip title={worksheet?.version || '（暂无）'} placement="top">
                            <Text 
                              className={`detail-field-content ${worksheet?.version ? 'has-value' : 'empty'}`}
                              style={{
                                color: worksheet?.version ? token.colorText : token.colorTextTertiary,
                              }}
                              onClick={startEditVersion}
                            >
                              {worksheet?.version || '（暂无）'}
                            </Text>
                          </Tooltip>
                        )}
                      </div>
                    </Col>

                    {/* 备注 */}
                    <Col span={6}>
                      <div className="detail-field-row">
                        <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                          备注
                        </Text>
                        {editingNotes ? (
                          <div className="detail-edit-container">
                            <Input
                              value={tempNotes}
                              onChange={(e) => setTempNotes(e.target.value)}
                              placeholder="添加备注..."
                              className="detail-edit-input"
                              autoFocus
                              onPressEnter={handleSaveNotes}
                              onKeyDown={(e) => {
                                if (e.key === 'Escape') {
                                  cancelEditNotes();
                                }
                              }}
                            />
                            <Button
                              type="primary"
                              size="small"
                              icon={<CheckOutlined />}
                              onClick={handleSaveNotes}
                            />
                            <Button
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={cancelEditNotes}
                            />
                          </div>
                        ) : (
                          <Tooltip title={worksheet?.notes || '（暂无）'} placement="top">
                            <Text 
                              className={`detail-field-content ${worksheet?.notes ? 'has-value' : 'empty'}`}
                              style={{
                                color: worksheet?.notes ? token.colorText : token.colorTextTertiary,
                              }}
                              onClick={startEditNotes}
                            >
                              {worksheet?.notes || '（暂无）'}
                            </Text>
                          </Tooltip>
                        )}
                      </div>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </div>
        </div>

        {/* 主体内容区域 */}
        <div className="detail-page-content">
          <Row gutter={[24, 24]} style={{ flex: 1, display: 'flex' }}>
            {/* 主要内容 */}
            <Col xs={24} style={{ display: 'flex', flexDirection: 'column' }}>
              {/* 标签页 */}
              <div style={{ 
                flex: 1,
                display: 'flex', 
                flexDirection: 'column' 
              }}>
                <Tabs
                  activeKey={activeTab}
                  onChange={handleTabChange}
                  destroyOnHidden={true}
                  style={{ 
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                  className={styles.fullHeightTabs}
                  items={[
                    {
                      key: 'background',
                      label: (
                        <span style={{ fontSize: '16px' }}>
                          <FileTextOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                          背景介绍
                        </span>
                      ),
                      children: <WorksheetBackgroundTab 
                        ref={worksheetBackgroundTabRef}
                        form={form} 
                        worksheetId={worksheet?.id} 
                        initialBgtext={worksheet?.bgtext || ''}
                        onSaveSuccess={handleBgtextSaveSuccess}
                      />,
                    },
                    {
                      key: 'units',
                      label: (
                        <span style={{ fontSize: '16px' }}>
                          <AppstoreOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                          单元题目
                        </span>
                      ),
                      children: (
                        <WorksheetUnitsTab
                          worksheetId={id}
                        />
                      ),
                    },
                  ]}
                />
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* 未保存更改确认对话框 */}
      <Modal
        title="未保存的更改"
        open={showUnsavedModal}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="discard" onClick={handleDiscardAndContinue}>
            不保存
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveAndContinue}>
            保存并继续
          </Button>,
        ]}
        closable={false}
        maskClosable={false}
      >
        <p>您有未保存的背景介绍内容更改，是否要保存？</p>
      </Modal>
    </div>
  );
};

export default WorksheetDetail; 