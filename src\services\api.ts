import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAuthStore } from '../utils/authStore';

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 防止重复登出的标志
let isLoggingOut = false;

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const { user } = useAuthStore.getState();

    // 如果有token，添加到请求头
    if (user && user.token) {
      config.headers.Authorization = `Bearer ${user.token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      const currentPath = window.location.pathname;
      const isLoginPage = currentPath.includes('/login');
      const { isAuthenticated } = useAuthStore.getState();
      
      // 只有当不在登录页面、用户当前是认证状态、且没有正在登出时才处理
      if (!isLoginPage && isAuthenticated && !isLoggingOut) {
        isLoggingOut = true;
        
        // 清除认证状态
        useAuthStore.getState().logout();
        
        // 延迟重定向，避免状态竞争
        setTimeout(() => {
          window.location.href = '/system/login';
          isLoggingOut = false;
        }, 100);
      }
    }

    // 对于500错误，确保错误信息能正确传递
    // 不在这里处理，让具体的调用者使用 errorHandler 来处理
    
    return Promise.reject(error);
  }
);

// 封装GET请求
export const get = <T>(url: string, params?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.get(url, { params, ...config }).then((res: AxiosResponse<T>) => res.data);
};

// 封装POST请求
export const post = <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.post(url, data, config).then((res: AxiosResponse<T>) => res.data);
};

// 封装PUT请求
export const put = <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.put(url, data, config)
    .then((res: AxiosResponse<T>) => res.data)
    .catch(error => {
      throw error;
    });
};

// 封装DELETE请求
export const del = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return api.delete(url, config).then((res: AxiosResponse<T>) => res.data);
};

export default api; 