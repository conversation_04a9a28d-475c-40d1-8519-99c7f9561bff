import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createQuestionGuide,
  updateQuestionGuide,
  type QuestionGuideResponse,
  type QuestionGuideCreate,
  type QuestionGuideUpdate
} from '../../../services/system/question';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface QuestionGuideFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  guide: QuestionGuideResponse | null;
  tenantId: number;
  questionId: number;
}

const QuestionGuideForm: React.FC<QuestionGuideFormProps> = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  guide, 
  tenantId, 
  questionId 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (guide) {
        form.setFieldsValue({
          title: guide.title,
          details: guide.details,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, guide, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (guide) {
        // 编辑模式
        const updateData: QuestionGuideUpdate = {
          title: values.title,
          details: values.details,
        };
        await updateQuestionGuide(guide.id, tenantId, updateData);
        showSuccess('更新问题指南成功');
      } else {
        // 创建模式
        const createData: QuestionGuideCreate = {
          tenant_id: tenantId,
          qid: questionId,
          title: values.title,
          details: values.details,
        };
        await createQuestionGuide(createData);
        showSuccess('创建问题指南成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={guide ? '编辑问题指南' : '添加问题指南'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="指南标题"
          name="title"
          rules={[
            { required: true, message: '请输入指南标题' },
          ]}
        >
          <Input placeholder="请输入指南标题" />
        </Form.Item>

        <Form.Item
          label="指南详情"
          name="details"
          rules={[
            { required: true, message: '请输入指南详情' },
          ]}
        >
          <TextArea 
            rows={6} 
            placeholder="请输入指南详情" 
            showCount 
            maxLength={2000}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuestionGuideForm;
