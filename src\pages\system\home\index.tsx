import React from 'react';
import { Card, Typography, Row, Col } from 'antd';
import { useAuthStore } from '../../../utils/authStore';

const { Title, Paragraph } = Typography;

const SystemHome: React.FC = () => {
  const { user } = useAuthStore();

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Title level={2} style={{ textAlign: 'center', marginBottom: '24px' }}>
              欢迎使用 TMAI 系统管理后台
            </Title>
            <div style={{ textAlign: 'center' }}>
              <Paragraph style={{ fontSize: '16px', color: '#666' }}>
                您好，{user?.name}！欢迎来到 TMAI 人工智能管理系统。
              </Paragraph>
              <Paragraph style={{ fontSize: '14px', color: '#999' }}>
                您可以通过左侧菜单访问各种管理功能，包括用户管理、机器人管理、租户管理等。
              </Paragraph>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemHome; 