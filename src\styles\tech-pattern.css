/* 科技感背景图案 */
.tech-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(rgba(100, 150, 255, 0.3) 2px, transparent 2px),
    radial-gradient(rgba(100, 150, 255, 0.2) 1px, transparent 1px);
  background-size: 50px 50px, 25px 25px;
  background-position: 0 0, 12.5px 12.5px;
  z-index: 0;
  animation: patternPulse 20s infinite ease-in-out;
}

@keyframes patternPulse {
  0%, 100% {
    opacity: 0.6;
    background-size: 50px 50px, 25px 25px;
  }
  50% {
    opacity: 0.8;
    background-size: 52px 52px, 26px 26px;
  }
}

/* 添加十字网格线 */
.tech-pattern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(100, 150, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(100, 150, 255, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
  z-index: 0;
}

 