import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore, UserType } from '../../utils/authStore';

interface RequireSystemAuthProps {
  children: React.ReactNode;
}

const RequireSystemAuth: React.FC<RequireSystemAuthProps> = ({ children }) => {
  const { isAuthenticated, userType, isLoading } = useAuthStore();
  const location = useLocation();

  // 如果还在加载中，显示加载状态
  if (isLoading) {
    return <div>加载中...</div>;
  }

  // 如果未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/system/login" state={{ from: location }} replace />;
  }

  // 如果不是系统管理员，重定向到登录页
  if (userType !== UserType.SYSTEM) {
    return <Navigate to="/system/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default RequireSystemAuth; 