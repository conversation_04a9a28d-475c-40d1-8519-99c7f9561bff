import { get, post, put, del } from '../api';

// 租户管理员信息接口
export interface TenantAdminResponse {
  id: number;
  tenant_id: number;
  uid: number;
  username: string;
  name: string;
  user?: string;
  active: number; // 0-禁用, 1-启用
  role: number; // 角色: 1-管理员, 2-运营人员
  token_version: number;
  ctime: string; // 创建时间
  utime?: string; // 更新时间
  tenant_code: string; // 租户编码
  tenant_name: string; // 租户名称
}

// 租户管理员列表响应接口
export interface TenantAdminListResponse {
  total: number;
  items: TenantAdminResponse[];
}

// 租户管理员创建接口
export interface TenantAdminCreate {
  tenant_id: number;
  username: string;
  password: string;
  name: string;
  user?: string;
  role: number; // 角色: 0-超级管理员, 1-管理员
}

// 租户管理员更新接口
export interface TenantAdminUpdate {
  tenant_id?: number;
  username?: string;
  password?: string;
  name?: string;
  user?: string;
  active?: number;
  role?: number; // 角色: 0-超级管理员, 1-管理员
}

// 查询参数接口
export interface TenantAdminQueryParams {
  skip?: number;
  limit?: number;
  username?: string; // 按用户名搜索，支持模糊匹配
  name?: string; // 按姓名搜索，支持模糊匹配
  tenant_id?: number; // 按租户ID筛选
  active?: number; // 按用户状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: string;
  sort_order?: string;
  start_time?: string;
  end_time?: string;
}

// 获取租户管理员列表
export const getTenantAdmins = async (params: TenantAdminQueryParams): Promise<TenantAdminListResponse> => {
  return await get<TenantAdminListResponse>('/sys/tnt-admin', params);
};

// 获取单个租户管理员
export const getTenantAdmin = async (adminId: number): Promise<TenantAdminResponse> => {
  return await get<TenantAdminResponse>(`/sys/tnt-admin/${adminId}`);
};

// 创建租户管理员
export const createTenantAdmin = async (admin: TenantAdminCreate): Promise<TenantAdminResponse> => {
  return await post<TenantAdminResponse>('/sys/tnt-admin', admin);
};

// 更新租户管理员
export const updateTenantAdmin = async (adminId: number, admin: TenantAdminUpdate): Promise<TenantAdminResponse> => {
  return await put<TenantAdminResponse>(`/sys/tnt-admin/${adminId}`, admin);
};

// 删除租户管理员
export const deleteTenantAdmin = async (adminId: number): Promise<void> => {
  return await del<void>(`/sys/tnt-admin/${adminId}`);
}; 