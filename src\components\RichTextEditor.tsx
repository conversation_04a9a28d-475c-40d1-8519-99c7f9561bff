import React, { useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Tooltip, Space, message, theme } from 'antd';
import { SaveOutlined, FullscreenOutlined, CompressOutlined } from '@ant-design/icons';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import styles from './RichTextEditor.module.css';

// 工具栏模式类型
export type ToolbarMode = 'minimal' | 'standard' | 'full';

// 不同模式的工具栏配置
const toolbarConfigs = {
  minimal: [
    ['bold', 'italic', 'underline'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['clean']
  ],
  standard: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'align': [] }],
    ['link', 'clean']
  ],
  full: [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    [{ 'font': [] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'script': 'sub'}, { 'script': 'super' }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'direction': 'rtl' }, { 'align': [] }],
    ['blockquote', 'code-block'],
    ['link', 'image', 'video'],
    ['clean']
  ]
};

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  title?: string;
  saveMessage?: string;
  saveTooltip?: string;
  fullscreenTooltip?: string;
  exitFullscreenTooltip?: string;
  onSave?: (value: string) => void;
  showSaveButton?: boolean;
  showFullscreenButton?: boolean;
  toolbarMode?: ToolbarMode;
  style?: React.CSSProperties;
  theme?: 'snow' | 'bubble';
}

export interface RichTextEditorRef {
  focus: () => void;
  blur: () => void;
  getQuillInstance: () => ReactQuill | null;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(({
  value,
  onChange,
  placeholder = "请输入内容...",
  title = "内容编辑",
  saveMessage,
  saveTooltip = "保存内容",
  fullscreenTooltip = "全屏编辑",
  exitFullscreenTooltip = "退出全屏",
  onSave,
  showSaveButton = true,
  showFullscreenButton = true,
  toolbarMode = 'full',
  style = {},
  theme: quillTheme = 'snow'
}, ref) => {
  const { token } = theme.useToken();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const quillRef = useRef<ReactQuill>(null);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (quillRef.current) {
        const editor = quillRef.current.getEditor();
        editor.focus();
      }
    },
    blur: () => {
      if (quillRef.current) {
        const editor = quillRef.current.getEditor();
        editor.blur();
      }
    },
    getQuillInstance: () => {
      return quillRef.current;
    }
  }));

  // 全屏切换函数
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 保存函数
  const handleSave = () => {
    if (onSave && value) {
      onSave(value);
    }
    // 只有在明确提供了saveMessage时才显示消息
    if (saveMessage) {
      message.success(saveMessage);
    }
  };

  // 处理内容变更
  const handleChange = (content: string) => {
    if (onChange) {
      onChange(content);
    }
  };



  // Quill 模块配置
  const modules = {
    toolbar: toolbarConfigs[toolbarMode]
  };

  // Quill 格式配置 - 基于 Quill 2.0 官方支持的格式
  const formats = [
    // Inline formats
    'background', 'bold', 'color', 'font', 'code', 'italic', 'link', 'size', 'strike', 'script', 'underline',
    // Block formats
    'blockquote', 'header', 'indent', 'list', 'align', 'direction', 'code-block',
    // Embeds
    'image', 'video'
  ];

  return (
    <div 
      className={`${styles.richTextEditorContainer} ${isFullscreen ? styles.fullscreenEditor : ''}`}
      style={{ 
        border: `1px solid ${token.colorBorder}`,
        borderRadius: token.borderRadius,
        overflow: 'hidden',
        backgroundColor: '#fff',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02)',
        ...style
      }}
    >
      {/* 自定义工具栏 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 12px',
        borderBottom: `1px solid ${token.colorBorder}`,
        backgroundColor: token.colorFillAlter
      }}>
        <div style={{ fontSize: '14px', color: token.colorTextSecondary }}>
          {title}
        </div>
        <Space>
          {showSaveButton && (
            <Tooltip title={saveTooltip}>
              <Button
                type="text"
                size="small"
                icon={<SaveOutlined />}
                onClick={handleSave}
              />
            </Tooltip>
          )}
          {showFullscreenButton && (
            <Tooltip title={isFullscreen ? exitFullscreenTooltip : fullscreenTooltip}>
              <Button
                type="text"
                size="small"
                icon={isFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
                onClick={toggleFullscreen}
              />
            </Tooltip>
          )}
        </Space>
      </div>
      
      {/* 富文本编辑器 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        overflow: 'hidden',
        minHeight: 0,
        width: '100%'
      }}>
        <ReactQuill
          ref={quillRef}
          theme={quillTheme}
          value={value || ''}
          onChange={handleChange}
          placeholder={placeholder}
          modules={modules}
          formats={formats}
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            width: '100%'
          }}
        />
      </div>
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor; 