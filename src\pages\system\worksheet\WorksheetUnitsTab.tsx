import React from 'react';
import { Empty } from 'antd';

interface WorksheetUnitsTabProps {
  worksheetId?: string;
}

const WorksheetUnitsTab: React.FC<WorksheetUnitsTabProps> = (/* { worksheetId } */) => {
  return (
    <div style={{ 
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '40px'
    }}>
      <Empty
        description="单元题目功能即将上线"
        style={{ 
          color: '#999'
        }}
      />
    </div>
  );
};

export default WorksheetUnitsTab; 