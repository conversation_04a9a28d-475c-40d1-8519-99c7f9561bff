import { get, post, put, del } from '../api';

// 模块响应数据类型
export interface ModuleResponse {
  id: number;
  tenant_id: number;
  fid?: number | null;  // 理论框架ID (API使用fid字段)
  name: string;
  description?: string | null;
  content?: string | null;
  active: number;
  priority?: number;
  ctime?: string;
  utime?: string;
}

// 创建模块数据类型
export interface ModuleCreate {
  tenant_id: number;
  fid?: number | null;  // 理论框架ID (API使用fid字段)
  name: string;
  description?: string;
  content?: string;
  active?: number;
}

// 更新模块数据类型
export interface ModuleUpdate {
  name?: string;
  description?: string;
  content?: string;
  fid?: number | null;  // 理论框架ID (API使用fid字段)
  active?: number;
}

// 分页响应类型
export interface ModuleListResponse {
  items: ModuleResponse[];
  total: number;
}

// 查询参数类型
export interface ModuleQuery {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number;
  framework_id?: number;
  sort_by?: 'id' | 'priority';
  sort_order?: 'asc' | 'desc';
}

// 获取模块列表
export const getModules = async (params: ModuleQuery): Promise<ModuleListResponse> => {
  return await get<ModuleListResponse>('/sys/module', params);
};

// 获取单个模块
export const getModule = async (id: number): Promise<ModuleResponse> => {
  return await get<ModuleResponse>(`/sys/module/${id}`);
};

// 创建模块
export const createModule = async (data: ModuleCreate): Promise<ModuleResponse> => {
  return await post<ModuleResponse>('/sys/module', data);
};

// 更新模块
export const updateModule = async (id: number, data: ModuleUpdate): Promise<ModuleResponse> => {
  return await put<ModuleResponse>(`/sys/module/${id}`, data);
};

// 删除模块
export const deleteModule = async (id: number): Promise<void> => {
  await del<void>(`/sys/module/${id}`);
};

// 批量更新模块顺序相关类型
export interface ModuleOrderItem {
  id: number;
  priority: number;
}

export interface ModuleBatchOrderRequest {
  tenant_id: number;
  modules: ModuleOrderItem[];
}

export interface ModuleBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_modules: ModuleResponse[];
}

// 批量更新模块顺序
export const batchUpdateModuleOrder = async (data: ModuleBatchOrderRequest): Promise<ModuleBatchOrderResponse> => {
  return await put<ModuleBatchOrderResponse>('/sys/module/batch/order', data);
}; 