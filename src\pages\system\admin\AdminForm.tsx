import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select } from 'antd';
import { 
  createAdmin, 
  updateAdmin,
  type SysAdminResponse,
  type SysAdminCreate,
  type SysAdminUpdate 
} from '../../../services/system/admin';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { encryptPassword } from '../../../utils/crypto';

interface AdminFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  admin: SysAdminResponse | null;
}

const AdminForm: React.FC<AdminFormProps> = ({ visible, onCancel, onSuccess, admin }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (admin) {
        form.setFieldsValue({
          username: admin.username,
          name: admin.name,
          role: typeof admin.role === 'number' ? admin.role : 1, // 确保role是数字类型，否则使用默认值
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, admin, form]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (admin) {
        const updateData: SysAdminUpdate = {
          username: values.username,
          name: values.name,
          role: values.role,
        };
        
        if (values.password && values.password.trim() !== '') {
          updateData.password = encryptPassword(values.password);
        }
        
        await updateAdmin(admin.id, updateData);
        showSuccess('更新管理员成功');
      } else {
        const createData: SysAdminCreate = {
          username: values.username,
          password: encryptPassword(values.password),
          name: values.name,
          role: values.role,
        };
        await createAdmin(createData);
        showSuccess('创建管理员成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={admin ? '编辑管理员' : '添加管理员'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ active: true, role: 1 }}
      >
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          name="name"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>

        <Form.Item
          name="role"
          label="角色"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Select placeholder="请选择角色">
            <Select.Option value={0}>超级管理员</Select.Option>
            <Select.Option value={1}>管理员</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: !admin, message: '请输入密码' }]}
        >
          <Input.Password placeholder={admin ? "留空则不修改密码" : "请输入密码"} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AdminForm; 