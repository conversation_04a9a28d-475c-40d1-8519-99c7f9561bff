import { get, post, put, del } from '../api';

// 机器人配置数据类型
export interface BotConfig {
  key: string;
  bid: number;
  bot_name: string; // 必需字段：机器人名称
  bot_active: number; // 必需字段：机器人是否有效（0：失效；1：有效）
  notes?: string; // 可选字段：备注
  bot_api_endpoint?: string; // 可选字段：机器人API路径
  bot_api_key?: string; // 可选字段：机器人API密钥
  bot_sys_prompt?: string; // 可选字段：机器人系统提示
  bot_notes?: string; // 可选字段：机器人备注
}

// 机器人配置创建请求
export interface BotConfigCreate {
  key: string;
  bid: number;
  notes?: string;
}

// 机器人配置更新请求
export interface BotConfigUpdate {
  key?: string; // 可选：配置键名
  bid?: number; // 可选：机器人ID
  notes?: string; // 可选：备注
}

// 机器人配置列表响应
export interface BotConfigListResponse {
  total: number;
  items: BotConfig[];
}

// 获取机器人配置列表
export const getBotConfigs = async (params: {
  skip?: number;
  limit?: number;
  keyword?: string;
}): Promise<BotConfigListResponse> => {
  return get('/sys/bconf', params);
};

// 创建机器人配置
export const createBotConfig = async (data: BotConfigCreate): Promise<BotConfig> => {
  return post('/sys/bconf', data);
};

// 获取机器人配置详情
export const getBotConfig = async (key: string): Promise<BotConfig> => {
  return get(`/sys/bconf/${key}`);
};

// 更新机器人配置
export const updateBotConfig = async (key: string, data: BotConfigUpdate): Promise<BotConfig> => {
  return put(`/sys/bconf/${key}`, data);
};

// 删除机器人配置
export const deleteBotConfig = async (key: string): Promise<void> => {
  return del(`/sys/bconf/${key}`);
}; 