import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import { 
  getBots, 
  deleteBot,
  updateBot,
  type Bot 
} from '../../../services/system/bot';
import { showError, showSuccess } from '../../../utils/errorHandler';
import BotForm from './BotForm';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;

const BotManagement: React.FC = () => {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [keywordValue, setKeywordValue] = useState(''); // 用于存储输入框的当前值
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentBot, setCurrentBot] = useState<Bot | null>(null);
  const [isRecycleBin, setIsRecycleBin] = useState(false);

  const [tableKey, setTableKey] = useState<number>(0);

  // 使用一个对象来存储所有列的排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc', // 默认按ID降序排序
    ctime: undefined
  });

  // 用于跟踪是否是首次渲染
  const isFirstRender = useRef(true);



  // 获取机器人列表
  const fetchBots = useCallback(async () => {
    try {
      setLoading(true);
      
      // 构建排序参数 - 只使用当前激活的排序字段
      let sortBy: string | undefined;
      let sortOrder: string | undefined;
      
      // 直接判断具体的排序字段，互斥处理
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getBots({
        skip: (current - 1) * pageSize,
        limit: pageSize,
        keyword: keyword || undefined,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
      });
      
      // 直接使用API返回的数据，无需客户端过滤
      setBots(response.items);
      
      // 更新分页信息
      setTotal(response.total);
    } catch (error) {
      showError(error, '获取机器人列表失败');
    } finally {
      setLoading(false);
    }
  }, [current, pageSize, keyword, isRecycleBin, sortOrders]);

  useEffect(() => {
    // 初次加载数据
    fetchBots();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 监听搜索条件变化，触发数据获取
  useEffect(() => {
    // 跳过组件初始挂载时的执行
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 手动触发数据获取
    fetchBots();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRecycleBin, keyword, sortOrders]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setKeyword(value);
    setKeywordValue(value);
  };

  // 处理搜索输入框值变化，但不触发搜索
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeywordValue(e.target.value);
  };

  // 处理添加机器人
  const handleAdd = () => {
    setCurrentBot(null);
    setIsModalVisible(true);
  };

  // 处理编辑机器人
  const handleEdit = (record: Bot) => {
    setCurrentBot(record);
    setIsModalVisible(true);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
  };



  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        sorter: true, // 改为简单的单一排序
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        render: (active: number) => (
          <span style={{ color: active === 1 ? 'green' : 'red' }}>
            {active === 1 ? '启用' : '禁用'}
          </span>
        ),
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: Bot) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 120,
        render: (_: unknown, record: Bot) => (
          <Space size="middle">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  // 处理禁用机器人（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = isRecycleBin ? '彻底删除' : '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个机器人吗？${isRecycleBin ? '此操作不可撤销。' : '禁用后可在回收站中恢复。'}`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (isRecycleBin) {
            // 在回收站模式下，执行真正的删除
            await deleteBot(id);
          } else {
            // 在正常模式下，将机器人禁用（设置active为0）
            await updateBot(id, { active: 0 });
          }
          showSuccess(`${actionText}成功`);
          fetchBots();
        } catch (error: unknown) {
          showError(error, `${actionText}机器人失败`);
        }
      },
    });
  };

  // 处理恢复机器人
  const handleRestore = async (botId: number) => {
    try {
      await updateBot(botId, { active: 1 });
      showSuccess('恢复成功');
      fetchBots();
    } catch (error) {
      showError(error, '恢复机器人失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setIsModalVisible(false);
    fetchBots();
  };

  // 处理重置刷新按钮点击 - 重置所有筛选条件并刷新数据
  const handleRefresh = async () => {
    // 暂时禁用 useEffect 触发的自动获取数据
    isFirstRender.current = true;

    // 清空表头上的搜索框
    setKeyword('');
    setKeywordValue('');

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setCurrent(1);
    setPageSize(10);

    try {
      // 直接调用接口获取数据，使用重置后的参数
      setLoading(true);
      const response = await getBots({
        skip: 0,
        limit: 10,
        keyword: undefined, // 重置后的搜索条件
        active: isRecycleBin ? 0 : 1, // 根据当前模式传递正确的active参数进行服务端过滤
        sort_by: 'id',      // 默认ID排序
        sort_order: 'desc', // 默认降序
      });
      
      // 直接使用API返回的数据，无需客户端过滤
      setBots(response.items);
      
      // 更新分页信息
      setTotal(response.total);

      // 强制重新渲染表格
      setTableKey(prev => prev + 1);

    } catch (error) {
      showError(error, '刷新数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格变化（分页、排序等）
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableChange = (paginationConfig: any, _filters: Record<string, unknown>, sorter: unknown) => {
    // 处理分页变化
    if (paginationConfig) {
      setCurrent(paginationConfig.current || 1);
      setPageSize(paginationConfig.pageSize || 10);
    }

    // 定义排序接口
    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    // 转换为单个sorter对象
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    // 重置所有排序状态
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    // 根据点击的列头设置对应的sortBy和sortOrder
    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        // 点击ID列头，sortBy=id，sortOrder为正序、逆序、取消排序轮询
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
        // order为undefined时表示取消排序，保持undefined
      } else if (field === 'ctime') {
        // 点击创建时间列头，sortBy=ctime，sortOrder也类似轮询
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
        // order为undefined时表示取消排序，保持undefined
      }
      
    }

    // 如果所有排序都被取消，则使用默认排序（ID 降序）
    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    // 更新排序状态
    setSortOrders(newSortOrders);
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '机器人管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "机器人回收站" : "机器人管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索名称"
              allowClear
              value={keywordValue}
              onChange={handleSearchInputChange}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />

            {!isRecycleBin && (
              <Tooltip title="添加机器人">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={bots}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: isRecycleBin ? '回收站中没有机器人' : '暂无数据'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      <BotForm
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onSuccess={handleFormSuccess}
        bot={currentBot}
      />
    </div>
  );
};

export default BotManagement; 