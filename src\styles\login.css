/* 登录页面样式 */
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  height: 100vh;
  overflow: auto;
  position: relative;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4ecf7 100%);
}

/* 添加动态科技感背景 */
.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(217deg, rgba(220, 240, 255, 0.05), rgba(220, 240, 255, 0) 70.71%),
    linear-gradient(127deg, rgba(200, 230, 255, 0.05), rgba(200, 230, 255, 0) 70.71%),
    linear-gradient(336deg, rgba(210, 235, 255, 0.05), rgba(210, 235, 255, 0) 70.71%);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: 0;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1; /* 确保内容在背景之上 */
}

/* 添加科技感网格背景 */
.login-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(100, 150, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(100, 150, 255, 0.15) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: 0;
}

.login-top {
  text-align: center;
  width: 368px; /* 与登录卡片宽度一致 */
  margin: 0 auto; /* 水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-header {
  height: 44px;
  line-height: 44px;
}

.login-logo {
  height: 44px;
  margin-right: 16px;
  vertical-align: top;
}

.login-title {
  position: relative;
  top: 2px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
  text-align: center;
  width: 100%;
}

.login-desc {
  margin-top: 12px;
  margin-bottom: 40px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  text-align: center;
  width: 100%;
}

.login-main {
  width: 368px;
  margin: 0 auto;
}

.login-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 0.8s ease-out;
}

.login-form-button {
  width: 100%;
}

/* 添加输入框焦点样式 */
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-form-margin {
  margin-bottom: 16px;
}

.login-form-forgot {
  float: right;
}

.login-other {
  margin-top: 24px;
  line-height: 22px;
  text-align: left;
}

.login-switch {
  margin-top: 24px;
  text-align: center;
}

.login-icon {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color 0.3s;
}

.login-icon:hover {
  color: #1890ff;
}

.login-footer {
  margin-top: 120px; /* 调整与login-card的垂直间距 */
  padding: 0;
  text-align: center;
  width: 368px; /* 与登录卡片宽度一致 */
  margin-left: auto; /* 水平居中 */
  margin-right: auto; /* 水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-footer-links {
  margin-bottom: 8px;
}

.login-footer-link {
  color: rgba(0, 0, 0, 0.45);
}

.login-footer-link:hover {
  color: rgba(0, 0, 0, 0.65);
}

.login-footer-copyright {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  width: 100%;
  text-align: center;
}

.login-card-flip {
  perspective: 1000px;
  width: 368px;
  height: 370px;
  margin: 0 auto; /* 水平居中 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  display: flex;
  justify-content: center;
}

.login-card-front,
.login-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-card-front {
  transform: rotateY(180deg);
}

.login-card-back {
  transform: rotateY(0deg);
}

.login-card-flip.flipped .login-card-inner {
  transform: rotateY(180deg);
}

.login-switch-button {
  margin-top: 16px;
  text-align: center;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-top,
  .login-main,
  .login-footer,
  .login-card-flip {
    width: 320px;
  }
  
  .login-title {
    font-size: 28px;
  }
  
  .login-card,
  .login-card-front,
  .login-card-back {
    padding: 25px 20px;
  }
}

@media (max-width: 480px) {
  .login-top,
  .login-main,
  .login-footer,
  .login-card-flip {
    width: 280px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .login-desc {
    font-size: 12px;
  }
  
  .login-card,
  .login-card-front,
  .login-card-back {
    padding: 20px 15px;
  }
}

 