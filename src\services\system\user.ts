import { get, post, put, del } from '../api';

// 用户数据类型 - 与API文档中的SysUserResponse保持一致
export interface SystemUser {
  id: number;
  username: string;
  name: string;
  user?: string; // 用户备注字段
  token_version?: number; // token版本号
  active: number; // 状态：0-禁用，1-启用
  ctime: string; // 创建时间
  utime?: string; // 更新时间
}

// 用户创建请求 - 与API文档中的SysUserCreate保持一致
export interface SystemUserCreate {
  username: string;
  password: string;
  name: string;
  user?: string;
  active?: number;
}

// 用户更新请求 - 与API文档中的SysUserUpdate保持一致
export interface SystemUserUpdate {
  username?: string;
  password?: string;
  name?: string;
  user?: string;
  active?: number;
}

// 用户列表响应 - 与API文档中的SysUserListResponse保持一致
export interface SystemUserListResponse {
  total: number;
  items: SystemUser[];
}

// 获取用户列表
export const getSystemUsers = async (params: {
  skip?: number;
  limit?: number;
  keyword?: string;
  active?: number; // 按用户状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: string;
  sort_order?: string;
  start_time?: string;
  end_time?: string;
}): Promise<SystemUserListResponse> => {
  return get('/sys/user', params);
};

// 创建用户
export const createSystemUser = async (data: SystemUserCreate): Promise<SystemUser> => {
  return post('/sys/user', data);
};

// 获取用户详情
export const getSystemUser = async (userId: number): Promise<SystemUser> => {
  return get(`/sys/user/${userId}`);
};

// 更新用户
export const updateSystemUser = async (userId: number, data: SystemUserUpdate): Promise<SystemUser> => {
  return put(`/sys/user/${userId}`, data);
};

// 删除用户
export const deleteSystemUser = async (userId: number): Promise<void> => {
  return del(`/sys/user/${userId}`);
}; 